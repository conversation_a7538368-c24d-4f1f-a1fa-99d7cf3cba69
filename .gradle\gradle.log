WARNING: You are using an unsupported version of ForgeGradle.
Please consider upgrading to ForgeGradle 4 and helping in the efforts to get old versions working on the modern toolchain.
See https://gist.github.com/TheCurle/fe7ad3ede188cbdd15c235cc75d52d4a for more info on contributing.
#################################################
         ForgeGradle 1.2.2-g2ea0336        
  https://github.com/MinecraftForge/ForgeGradle  
#################################################
               Powered by MCP snapshot_20141001               
             http://modcoderpack.com             
         by: Searge, ProfMobius, Fesh0r,         
         R4wk, Zeu<PERSON>, IngisKahn, bspkrs           
#################################################
:tasks

------------------------------------------------------------
All tasks runnable from root project
------------------------------------------------------------

Build tasks
-----------
apiClasses - Assembles classes 'api'.
    applyBinPatches
    compileApiJava - Compiles Java source 'api:java'.
    deobfBinJar
    downloadClient
    downloadMcpTools
    downloadServer
    extractMcpData
    extractUserDev
    genSrgs
    getVersionJson
    mergeJars
    processApiResources - Processes resources 'api:resources'.
assemble - Assembles the outputs of this project. [jar]
    reobf
build - Assembles and tests this project. [assemble, check]
buildDependents - Assembles and tests this project and all projects that depend on it. [build]
buildNeeded - Assembles and tests this project and all projects it depends on. [build]
classes - Assembles classes 'main'. [apiClasses]
    compileJava - Compiles Java source 'main:java'.
    processResources - Processes resources 'main:resources'.
    sourceMainJava
clean - Deletes the build directory.
jar - Assembles a jar archive containing the main classes. [classes]
testClasses - Assembles classes 'test'. [apiClasses, classes]
    compileTestJava - Compiles Java source 'test:java'.
    processTestResources - Processes resources 'test:resources'.

Build Setup tasks
-----------------
init - Initializes a new Gradle build. [incubating]
wrapper - Generates Gradle wrapper files. [incubating]

Documentation tasks
-------------------
javadoc - Generates Javadoc API documentation for the main source code. [classes]

ForgeGradle tasks
-----------------
cleanCache - Cleares the ForgeGradle cache. DONT RUN THIS unless you want a fresh start, or the dev tells you to.
debugClient - Runs the Minecraft client in debug mode [jar]
    extractNatives
    getAssets
    getAssetsIndex
    makeStart
debugServer - Runs the Minecraft serevr in debug mode [jar]
    extractNatives
    getAssets
    getAssetsIndex
    makeStart
runClient - Runs the Minecraft client [jar]
    extractNatives
    getAssets
    getAssetsIndex
    makeStart
runServer - Runs the Minecraft Server [jar]
    extractNatives
    getAssets
    getAssetsIndex
    makeStart
setupCIWorkspace - Sets up the bare minimum to build a minecraft mod. Idea for CI servers
    applyBinPatches
    deobfBinJar
    downloadClient
    downloadMcpTools
    downloadServer
    extractMcpData
    extractUserDev
    genSrgs
    getVersionJson
    mergeJars
setupDecompWorkspace - DevWorkspace + the deobfuscated Minecraft source linked as a source jar.
    decompile
    deobfuscateJar
    downloadClient
    downloadMcpTools
    downloadServer
    extractMcpData
    extractMinecraftSrc
    extractNatives
    extractUserDev
    genSrgs
    getAssets
    getAssetsIndex
    getVersionJson
    makeStart
    mergeJars
    processSources
    recompMinecraft
    remapJar
    repackMinecraft
setupDevWorkspace - CIWorkspace + natives and assets to run and test Minecraft
    applyBinPatches
    deobfBinJar
    downloadClient
    downloadMcpTools
    downloadServer
    extractMcpData
    extractNatives
    extractUserDev
    genSrgs
    getAssets
    getAssetsIndex
    getVersionJson
    makeStart
    mergeJars

Help tasks
----------
dependencies - Displays all dependencies declared in root project 'NM'.
dependencyInsight - Displays the insight into a specific dependency in root project 'NM'.
help - Displays a help message
projects - Displays the sub-projects of root project 'NM'.
properties - Displays the properties of root project 'NM'.
tasks - Displays the tasks runnable from root project 'NM'.

IDE tasks
---------
cleanEclipse - Cleans all Eclipse files.
    cleanEclipseClasspath
    cleanEclipseJdt
    cleanEclipseProject
cleanIdea - Cleans IDEA project files (IML, IPR)
    cleanIdeaModule
    cleanIdeaProject
eclipse - Generates all Eclipse files.
    eclipseClasspath - Generates the Eclipse classpath file.
    eclipseJdt - Generates the Eclipse JDT settings file.
    eclipseProject - Generates the Eclipse project file.
idea - Generates IDEA project files (IML, IPR, IWS)
    ideaModule - Generates IDEA module files (IML)
    ideaProject - Generates IDEA project file (IPR)
    ideaWorkspace - Generates an IDEA workspace file (IWS)

Upload tasks
------------
uploadArchives - Uploads all artifacts belonging to configuration ':archives' [jar]
    reobf

Verification tasks
------------------
check - Runs all checks. [test]
test - Runs the unit tests. [classes, testClasses]

Other tasks
-----------
cleanIdeaWorkspace
genIntellijRuns
install - Installs the 'archives' artifacts into the local Maven repository. [jar]

Rules
-----
Pattern: clean<TaskName>: Cleans the output files of a task.
Pattern: build<ConfigurationName>: Assembles the artifacts of a configuration.
Pattern: upload<ConfigurationName>: Assembles and uploads the artifacts belonging to a configuration.

BUILD SUCCESSFUL

Total time: 19.321 secs
