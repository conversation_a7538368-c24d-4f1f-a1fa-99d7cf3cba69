WARNING: You are using an unsupported version of ForgeGradle.
Please consider upgrading to ForgeGradle 4 and helping in the efforts to get old versions working on the modern toolchain.
See https://gist.github.com/TheCurle/fe7ad3ede188cbdd15c235cc75d52d4a for more info on contributing.
#################################################
         ForgeGradle 1.2.2-g2ea0336        
  https://github.com/MinecraftForge/ForgeGradle  
#################################################
               Powered by MCP snapshot_20141001               
             http://modcoderpack.com             
         by: <PERSON><PERSON>, Prof<PERSON><PERSON>ius, Fesh0<PERSON>,         
         R4wk, Z<PERSON><PERSON>, Ingis<PERSON>ahn, bspkrs           
#################################################
:extractMcpData UP-TO-DATE
:getVersionJsonhttp://s3.amazonaws.com/Minecraft.Download/versions/1.8/1.8.json  404'ed!

:extractUserDev

FAILURE: Build failed with an exception.

* What went wrong:
Could not resolve all dependencies for configuration ':userDevPackageDepConfig'.
> Could not resolve net.minecraftforge:forge:1.8-11.14.3.1502-1.8.
  Required by:
      com.newera:NM:1.0.0
   > Could not GET 'http://repo1.maven.org/maven2/net/minecraftforge/forge/1.8-11.14.3.1502-1.8/forge-1.8-11.14.3.1502-1.8.pom'. Received status code 501 from server: HTTPS Required
   > Could not GET 'http://repo1.maven.org/maven2/net/minecraftforge/forge/1.8-11.14.3.1502-1.8/forge-1.8-11.14.3.1502-1.8.pom'. Received status code 501 from server: HTTPS Required

* Try:
Run with --stacktrace option to get the stack trace. Run with --info or --debug option to get more log output.

BUILD FAILED

Total time: 8.041 secs
