package com.newera.utils;

import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.*;

/**
 * Utility class for pathfinding and movement
 */
public class PathUtils {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    /**
     * Simple pathfinding using A* algorithm
     */
    public static List<BlockPos> findPath(BlockPos start, BlockPos end, int maxDistance) {
        World world = mc.theWorld;
        if (world == null) return new ArrayList<>();
        
        // Simple implementation - for production use, consider more sophisticated pathfinding
        List<BlockPos> path = new ArrayList<>();
        
        // Direct line approach for simplicity
        Vec3 startVec = new Vec3(start.getX(), start.getY(), start.getZ());
        Vec3 endVec = new Vec3(end.getX(), end.getY(), end.getZ());
        
        double distance = startVec.distanceTo(endVec);
        if (distance > maxDistance) {
            return path; // Too far
        }
        
        int steps = (int) Math.ceil(distance);
        for (int i = 0; i <= steps; i++) {
            double progress = (double) i / steps;
            
            int x = (int) (startVec.xCoord + (endVec.xCoord - startVec.xCoord) * progress);
            int y = (int) (startVec.yCoord + (endVec.yCoord - startVec.yCoord) * progress);
            int z = (int) (startVec.zCoord + (endVec.zCoord - startVec.zCoord) * progress);
            
            BlockPos pos = new BlockPos(x, y, z);
            if (BlockUtils.isSafePosition(pos)) {
                path.add(pos);
            }
        }
        
        return path;
    }
    
    /**
     * Find the closest accessible position to a target
     */
    public static BlockPos findAccessiblePosition(BlockPos target, int searchRadius) {
        World world = mc.theWorld;
        if (world == null) return null;
        
        // Check if target is already accessible
        if (BlockUtils.isSafePosition(target) && BlockUtils.canReachBlock(target)) {
            return target;
        }
        
        // Search in expanding radius
        for (int radius = 1; radius <= searchRadius; radius++) {
            for (int x = -radius; x <= radius; x++) {
                for (int z = -radius; z <= radius; z++) {
                    for (int y = -1; y <= 1; y++) { // Check a bit above and below
                        BlockPos pos = target.add(x, y, z);
                        
                        if (BlockUtils.isSafePosition(pos) && BlockUtils.canReachBlock(pos)) {
                            return pos;
                        }
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * Move player towards a position
     */
    public static void moveTowards(BlockPos target) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return;
        
        Vec3 playerPos = player.getPositionVector();
        Vec3 targetPos = new Vec3(target.getX() + 0.5, target.getY(), target.getZ() + 0.5);
        
        Vec3 direction = targetPos.subtract(playerPos).normalize();
        
        // Set movement input
        float forward = 0;
        float strafe = 0;
        
        // Calculate movement based on player's rotation
        float yaw = player.rotationYaw;
        float yawRad = (float) Math.toRadians(yaw);
        
        float forwardComponent = (float) (direction.xCoord * Math.sin(yawRad) + direction.zCoord * Math.cos(yawRad));
        float strafeComponent = (float) (direction.xCoord * Math.cos(yawRad) - direction.zCoord * Math.sin(yawRad));
        
        if (forwardComponent > 0.1) forward = 1;
        else if (forwardComponent < -0.1) forward = -1;
        
        if (strafeComponent > 0.1) strafe = 1;
        else if (strafeComponent < -0.1) strafe = -1;
        
        // Apply movement
        mc.gameSettings.keyBindForward.pressed = forward > 0;
        mc.gameSettings.keyBindBack.pressed = forward < 0;
        mc.gameSettings.keyBindLeft.pressed = strafe < 0;
        mc.gameSettings.keyBindRight.pressed = strafe > 0;
    }
    
    /**
     * Stop all movement
     */
    public static void stopMovement() {
        mc.gameSettings.keyBindForward.pressed = false;
        mc.gameSettings.keyBindBack.pressed = false;
        mc.gameSettings.keyBindLeft.pressed = false;
        mc.gameSettings.keyBindRight.pressed = false;
        mc.gameSettings.keyBindJump.pressed = false;
        mc.gameSettings.keyBindSneak.pressed = false;
    }
    
    /**
     * Check if player has reached a position
     */
    public static boolean hasReachedPosition(BlockPos target, double tolerance) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        double distance = player.getDistanceSq(target);
        return distance <= tolerance * tolerance;
    }
    
    /**
     * Get the distance to a position
     */
    public static double getDistanceToPosition(BlockPos pos) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return Double.MAX_VALUE;
        
        return player.getDistance(pos.getX(), pos.getY(), pos.getZ());
    }
    
    /**
     * Find the optimal farming path through a list of crop positions
     */
    public static List<BlockPos> optimizeFarmingPath(List<BlockPos> crops) {
        if (crops.isEmpty()) return new ArrayList<>();
        
        EntityPlayer player = mc.thePlayer;
        if (player == null) return crops;
        
        // Simple nearest neighbor approach
        List<BlockPos> optimizedPath = new ArrayList<>();
        List<BlockPos> remaining = new ArrayList<>(crops);
        
        BlockPos current = new BlockPos(player.posX, player.posY, player.posZ);
        
        while (!remaining.isEmpty()) {
            BlockPos closest = null;
            double closestDistance = Double.MAX_VALUE;
            
            for (BlockPos crop : remaining) {
                double distance = current.distanceSq(crop);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closest = crop;
                }
            }
            
            if (closest != null) {
                optimizedPath.add(closest);
                remaining.remove(closest);
                current = closest;
            }
        }
        
        return optimizedPath;
    }
    
    /**
     * Find the optimal mining path through a list of ore positions
     */
    public static List<BlockPos> optimizeMiningPath(List<BlockPos> ores) {
        return optimizeFarmingPath(ores); // Same algorithm works for mining
    }
    
    /**
     * Check if a position is within walking distance
     */
    public static boolean isWithinWalkingDistance(BlockPos pos, int maxDistance) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        double distance = player.getDistance(pos.getX(), pos.getY(), pos.getZ());
        return distance <= maxDistance;
    }
    
    /**
     * Get all positions within walking distance
     */
    public static List<BlockPos> getPositionsWithinDistance(List<BlockPos> positions, int maxDistance) {
        List<BlockPos> filtered = new ArrayList<>();
        
        for (BlockPos pos : positions) {
            if (isWithinWalkingDistance(pos, maxDistance)) {
                filtered.add(pos);
            }
        }
        
        return filtered;
    }
    
    /**
     * Calculate the total path distance
     */
    public static double calculatePathDistance(List<BlockPos> path) {
        if (path.size() < 2) return 0;
        
        double totalDistance = 0;
        for (int i = 1; i < path.size(); i++) {
            BlockPos prev = path.get(i - 1);
            BlockPos current = path.get(i);
            totalDistance += Math.sqrt(prev.distanceSq(current));
        }
        
        return totalDistance;
    }
    
    /**
     * Check if the player needs to jump to reach a position
     */
    public static boolean needsJump(BlockPos target) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);
        return target.getY() > playerPos.getY();
    }
    
    /**
     * Perform a jump if needed
     */
    public static void jumpIfNeeded(BlockPos target) {
        if (needsJump(target)) {
            mc.gameSettings.keyBindJump.pressed = true;
        }
    }
    
    /**
     * Get the next position in a path
     */
    public static BlockPos getNextPathPosition(List<BlockPos> path, BlockPos currentTarget) {
        if (path.isEmpty()) return null;
        
        int currentIndex = path.indexOf(currentTarget);
        if (currentIndex >= 0 && currentIndex < path.size() - 1) {
            return path.get(currentIndex + 1);
        }
        
        return path.get(0); // Return first position if not found
    }
}
