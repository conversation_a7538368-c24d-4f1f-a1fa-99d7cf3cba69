package com.newera.modules;

import com.newera.core.ConfigManager;
import com.newera.utils.InventoryUtils;
import com.newera.utils.RotationUtils;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.MovingObjectPosition;
import net.minecraft.util.Vec3;
import net.minecraftforge.common.MinecraftForge;

import java.util.ArrayList;
import java.util.List;

/**
 * Combat automation module
 * Handles automatic target selection, rotation, and attacking
 */
public class CombatModule extends BaseModule {
    
    private final ConfigManager.CombatConfig config;
    
    // Combat state
    private EntityLivingBase currentTarget = null;
    private long lastAttackTime = 0;
    private long lastTargetScanTime = 0;
    private final long TARGET_SCAN_INTERVAL = 500; // Scan for targets every 500ms
    
    public CombatModule(ConfigManager configManager) {
        super(configManager);
        this.config = configManager.getCombatConfig();
        setActionDelay(config.attackDelay);
    }
    
    @Override
    protected void registerEventHandlers() {
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    @Override
    protected void unregisterEventHandlers() {
        MinecraftForge.EVENT_BUS.unregister(this);
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        currentTarget = null;
        lastAttackTime = 0;
        lastTargetScanTime = 0;
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        currentTarget = null;
    }
    
    @Override
    public void onTick() {
        if (!isInGame()) return;
        
        updateTiming();
        
        // Scan for targets periodically
        if (System.currentTimeMillis() - lastTargetScanTime > TARGET_SCAN_INTERVAL) {
            scanForTargets();
            lastTargetScanTime = System.currentTimeMillis();
        }
        
        // Handle current target
        if (currentTarget != null) {
            handleCombat();
        }
        
        // Update smooth rotation
        RotationUtils.updateSmoothRotation();
    }
    
    /**
     * Scan for valid targets in range
     */
    private void scanForTargets() {
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        // Clear current target if it's invalid
        if (currentTarget != null && !isValidTarget(currentTarget)) {
            currentTarget = null;
        }
        
        // Find new target if we don't have one
        if (currentTarget == null) {
            currentTarget = findBestTarget();
            if (currentTarget != null) {
                debugLog("New target acquired: " + currentTarget.getName());
            }
        }
    }
    
    /**
     * Find the best target to attack
     */
    private EntityLivingBase findBestTarget() {
        EntityPlayer player = getPlayer();
        if (player == null) return null;
        
        List<EntityLivingBase> validTargets = new ArrayList<>();
        
        // Scan all entities in the world
        for (Object entity : getWorld().loadedEntityList) {
            if (entity instanceof EntityLivingBase) {
                EntityLivingBase livingEntity = (EntityLivingBase) entity;
                
                if (isValidTarget(livingEntity)) {
                    validTargets.add(livingEntity);
                }
            }
        }
        
        // Return the closest valid target
        if (!validTargets.isEmpty()) {
            EntityLivingBase closest = null;
            double closestDistance = Double.MAX_VALUE;
            
            for (EntityLivingBase target : validTargets) {
                double distance = player.getDistanceToEntity(target);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closest = target;
                }
            }
            
            return closest;
        }
        
        return null;
    }
    
    /**
     * Check if an entity is a valid target
     */
    private boolean isValidTarget(EntityLivingBase entity) {
        EntityPlayer player = getPlayer();
        if (player == null || entity == null) return false;
        
        // Don't target the player
        if (entity == player) return false;
        
        // Don't target dead entities
        if (entity.isDead || entity.getHealth() <= 0) return false;
        
        // Check distance
        double distance = player.getDistanceToEntity(entity);
        if (distance > config.range) return false;
        
        // Check if it's a mob we want to target
        if (entity instanceof EntityMob) {
            String mobName = entity.getClass().getSimpleName().replace("Entity", "");
            if (!config.targetMobs.contains(mobName)) {
                return false;
            }
        } else {
            // Only target mobs for now
            return false;
        }
        
        // Check line of sight if enabled
        if (config.lineOfSight && !hasLineOfSight(entity)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Handle combat with current target
     */
    private void handleCombat() {
        if (currentTarget == null) return;
        
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        // Check if target is still valid
        if (!isValidTarget(currentTarget)) {
            currentTarget = null;
            return;
        }
        
        // Switch to weapon
        int weaponSlot = config.weaponSlot >= 0 ? config.weaponSlot : InventoryUtils.findBestWeapon();
        if (weaponSlot >= 0) {
            InventoryUtils.switchToSlot(weaponSlot);
        }
        
        // Rotate towards target
        float[] rotations = RotationUtils.getRotationsToEntity(currentTarget);
        
        // Add slight jitter for more human-like aiming
        rotations = RotationUtils.addRotationJitter(rotations, 1.0f);
        
        RotationUtils.smoothRotateTo(rotations[0], rotations[1]);
        
        // Attack if we're looking at the target and cooldown is ready
        if (canAttack() && isLookingAtTarget()) {
            attackTarget();
        }
    }
    
    /**
     * Check if we can attack (cooldown check)
     */
    private boolean canAttack() {
        return System.currentTimeMillis() - lastAttackTime >= config.attackDelay;
    }
    
    /**
     * Check if we're looking at the current target
     */
    private boolean isLookingAtTarget() {
        if (currentTarget == null) return false;
        
        // Check if we're looking at the target within a tolerance
        return RotationUtils.isLookingAtEntity(currentTarget, 5.0f) && !RotationUtils.isRotating();
    }
    
    /**
     * Attack the current target
     */
    private void attackTarget() {
        if (currentTarget == null) return;
        
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        // Perform the attack
        mc.playerController.attackEntity(player, currentTarget);
        player.swingItem();
        
        lastAttackTime = System.currentTimeMillis();
        
        debugLog("Attacked " + currentTarget.getName() + " (Health: " + currentTarget.getHealth() + ")");
        
        // Check if target died
        if (currentTarget.isDead || currentTarget.getHealth() <= 0) {
            debugLog("Target eliminated: " + currentTarget.getName());
            currentTarget = null;
        }
    }
    
    /**
     * Check line of sight to an entity
     */
    private boolean hasLineOfSight(EntityLivingBase entity) {
        EntityPlayer player = getPlayer();
        if (player == null || entity == null) return false;
        
        Vec3 playerEyes = player.getPositionEyes(1.0f);
        Vec3 entityPos = new Vec3(entity.posX, entity.posY + entity.getEyeHeight(), entity.posZ);
        
        MovingObjectPosition result = getWorld().rayTraceBlocks(playerEyes, entityPos, false, true, false);
        
        // If rayTrace returns null, there's a clear line of sight
        // If it hits something, check if it's the target entity
        return result == null || result.entityHit == entity;
    }
    
    /**
     * Get the current target
     */
    public EntityLivingBase getCurrentTarget() {
        return currentTarget;
    }
    
    /**
     * Set a specific target (for external modules)
     */
    public void setTarget(EntityLivingBase target) {
        if (isValidTarget(target)) {
            currentTarget = target;
            debugLog("Target set externally: " + target.getName());
        }
    }
    
    /**
     * Clear the current target
     */
    public void clearTarget() {
        if (currentTarget != null) {
            debugLog("Target cleared: " + currentTarget.getName());
            currentTarget = null;
        }
    }
    
    /**
     * Check if the module is currently in combat
     */
    public boolean isInCombat() {
        return currentTarget != null && isValidTarget(currentTarget);
    }
    
    /**
     * Get the distance to current target
     */
    public double getDistanceToTarget() {
        if (currentTarget == null) return Double.MAX_VALUE;
        
        EntityPlayer player = getPlayer();
        if (player == null) return Double.MAX_VALUE;
        
        return player.getDistanceToEntity(currentTarget);
    }
    
    /**
     * Force a target scan
     */
    public void forceScan() {
        scanForTargets();
    }
    
    /**
     * Get all valid targets in range
     */
    public List<EntityLivingBase> getAllValidTargets() {
        List<EntityLivingBase> targets = new ArrayList<>();
        
        for (Object entity : getWorld().loadedEntityList) {
            if (entity instanceof EntityLivingBase) {
                EntityLivingBase livingEntity = (EntityLivingBase) entity;
                if (isValidTarget(livingEntity)) {
                    targets.add(livingEntity);
                }
            }
        }
        
        return targets;
    }
}
