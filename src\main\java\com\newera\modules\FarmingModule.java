package com.newera.modules;

import com.newera.core.ConfigManager;
import com.newera.utils.BlockUtils;
import com.newera.utils.InventoryUtils;
import com.newera.utils.PathUtils;
import com.newera.utils.RotationUtils;
import net.minecraft.block.Block;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.Item;
import net.minecraft.item.ItemSeeds;
import net.minecraft.item.ItemStack;
import net.minecraft.util.BlockPos;
import net.minecraftforge.common.MinecraftForge;

import java.util.ArrayList;
import java.util.List;

/**
 * Farming automation module
 * Handles automatic crop harvesting, replanting, and farm navigation
 */
public class FarmingModule extends BaseModule {
    
    private final ConfigManager.FarmingConfig config;
    
    // Farming state
    private FarmingState state = FarmingState.SCANNING;
    private List<BlockPos> farmingPath = new ArrayList<>();
    private int currentPathIndex = 0;
    private BlockPos currentTarget = null;
    private long lastHarvestTime = 0;
    
    private enum FarmingState {
        SCANNING,
        MOVING_TO_CROP,
        HARVESTING,
        REPLANTING
    }
    
    public FarmingModule(ConfigManager configManager) {
        super(configManager);
        this.config = configManager.getFarmingConfig();
        setActionDelay(500); // 500ms between actions
    }
    
    @Override
    protected void registerEventHandlers() {
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    @Override
    protected void unregisterEventHandlers() {
        MinecraftForge.EVENT_BUS.unregister(this);
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        state = FarmingState.SCANNING;
        farmingPath.clear();
        currentPathIndex = 0;
        currentTarget = null;
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        PathUtils.stopMovement();
    }
    
    @Override
    public void onTick() {
        if (!isInGame()) return;
        
        updateTiming();
        
        // Main farming logic
        switch (state) {
            case SCANNING:
                handleScanningState();
                break;
            case MOVING_TO_CROP:
                handleMovingState();
                break;
            case HARVESTING:
                handleHarvestingState();
                break;
            case REPLANTING:
                handleReplantingState();
                break;
        }
        
        // Update smooth rotation
        RotationUtils.updateSmoothRotation();
    }
    
    /**
     * Handle scanning for crops to harvest
     */
    private void handleScanningState() {
        if (!canPerformAction()) return;
        
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);
        
        // Find all fully grown crops in range
        List<BlockPos> crops = BlockUtils.getFullyGrownCrops(playerPos, config.range, config.targetCrops);
        
        if (!crops.isEmpty()) {
            // Optimize the farming path
            farmingPath = PathUtils.optimizeFarmingPath(crops);
            currentPathIndex = 0;
            
            if (!farmingPath.isEmpty()) {
                currentTarget = farmingPath.get(0);
                state = FarmingState.MOVING_TO_CROP;
                debugLog("Found " + crops.size() + " crops to harvest, starting with " + currentTarget);
            }
        } else {
            debugLog("No fully grown crops found in range");
        }
        
        updateActionTime();
    }
    
    /**
     * Handle moving to the next crop
     */
    private void handleMovingState() {
        if (currentTarget == null || farmingPath.isEmpty()) {
            state = FarmingState.SCANNING;
            return;
        }
        
        // Check if we've reached the target
        if (PathUtils.hasReachedPosition(currentTarget, 2.0)) {
            // Switch to farming tool
            int toolSlot = config.toolSlot >= 0 ? config.toolSlot : findFarmingTool();
            if (toolSlot >= 0) {
                InventoryUtils.switchToSlot(toolSlot);
            }
            
            state = FarmingState.HARVESTING;
            debugLog("Reached crop at " + currentTarget + ", starting harvest");
        } else {
            // Move towards the target
            PathUtils.moveTowards(currentTarget);
            
            // Look towards the target
            RotationUtils.smoothRotateTo(
                RotationUtils.getRotationsToBlock(currentTarget)[0],
                RotationUtils.getRotationsToBlock(currentTarget)[1]
            );
        }
    }
    
    /**
     * Handle harvesting the current crop
     */
    private void handleHarvestingState() {
        if (currentTarget == null) {
            state = FarmingState.SCANNING;
            return;
        }
        
        // Check if crop is still there and fully grown
        if (!BlockUtils.isCropFullyGrown(currentTarget)) {
            debugLog("Crop no longer fully grown or missing");
            moveToNextCrop();
            return;
        }
        
        // Look at the crop
        RotationUtils.smoothRotateTo(
            RotationUtils.getRotationsToBlock(currentTarget)[0],
            RotationUtils.getRotationsToBlock(currentTarget)[1]
        );
        
        // Harvest if we're looking at it and cooldown is ready
        if (!RotationUtils.isRotating() && 
            RotationUtils.isLookingAtBlock(currentTarget, 5.0f) && 
            canPerformAction()) {
            
            harvestCrop();
        }
    }
    
    /**
     * Handle replanting after harvest
     */
    private void handleReplantingState() {
        if (currentTarget == null) {
            moveToNextCrop();
            return;
        }
        
        // Check if we have seeds to replant
        ItemStack seeds = findSeedsForCrop(currentTarget);
        if (seeds == null) {
            debugLog("No seeds available for replanting");
            moveToNextCrop();
            return;
        }
        
        // Switch to seeds
        int seedSlot = findSeedSlot(seeds.getItem());
        if (seedSlot >= 0) {
            InventoryUtils.switchToSlot(seedSlot);
            
            // Plant the seeds
            if (canPerformAction()) {
                plantSeeds();
            }
        } else {
            moveToNextCrop();
        }
    }
    
    /**
     * Harvest the current crop
     */
    private void harvestCrop() {
        if (currentTarget == null) return;
        
        // Left click to break the crop
        mc.playerController.onPlayerDamageBlock(currentTarget, mc.objectMouseOver.sideHit);
        getPlayer().swingItem();
        
        lastHarvestTime = System.currentTimeMillis();
        updateActionTime();
        
        debugLog("Harvested crop at " + currentTarget);
        
        // Move to replanting if enabled
        if (config.replant) {
            state = FarmingState.REPLANTING;
        } else {
            moveToNextCrop();
        }
    }
    
    /**
     * Plant seeds at current position
     */
    private void plantSeeds() {
        if (currentTarget == null) return;
        
        // Right click to plant seeds
        mc.rightClickMouse();
        updateActionTime();
        
        debugLog("Replanted at " + currentTarget);
        moveToNextCrop();
    }
    
    /**
     * Move to the next crop in the path
     */
    private void moveToNextCrop() {
        currentPathIndex++;
        
        if (currentPathIndex < farmingPath.size()) {
            currentTarget = farmingPath.get(currentPathIndex);
            state = FarmingState.MOVING_TO_CROP;
            debugLog("Moving to next crop: " + currentTarget);
        } else {
            // Finished the current path, scan for more crops
            farmingPath.clear();
            currentPathIndex = 0;
            currentTarget = null;
            state = FarmingState.SCANNING;
            debugLog("Finished farming path, scanning for more crops");
        }
        
        PathUtils.stopMovement();
    }
    
    /**
     * Find appropriate farming tool
     */
    private int findFarmingTool() {
        // Look for hoe first, then any tool
        int hoeSlot = InventoryUtils.findHoe();
        if (hoeSlot >= 0) return hoeSlot;
        
        // If no hoe, use hand (slot 0 is fine)
        return -1; // Use current item
    }
    
    /**
     * Find seeds for a specific crop type
     */
    private ItemStack findSeedsForCrop(BlockPos cropPos) {
        if (getWorld() == null) return null;
        
        Block cropBlock = getWorld().getBlockState(cropPos).getBlock();
        String cropName = Block.blockRegistry.getNameForObject(cropBlock).toString();
        
        EntityPlayer player = getPlayer();
        if (player == null) return null;
        
        // Look for appropriate seeds in inventory
        for (int i = 0; i < player.inventory.getSizeInventory(); i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() instanceof ItemSeeds) {
                // Simple matching - in a real implementation, you'd want more sophisticated matching
                if (cropName.contains("wheat") && stack.getItem().getUnlocalizedName().contains("wheat")) {
                    return stack;
                } else if (cropName.contains("carrot") && stack.getItem().getUnlocalizedName().contains("carrot")) {
                    return stack;
                } else if (cropName.contains("potato") && stack.getItem().getUnlocalizedName().contains("potato")) {
                    return stack;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Find slot containing specific seeds
     */
    private int findSeedSlot(Item seedItem) {
        EntityPlayer player = getPlayer();
        if (player == null) return -1;
        
        for (int i = 0; i < 9; i++) { // Check hotbar only
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() == seedItem) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * Get current farming target
     */
    public BlockPos getCurrentTarget() {
        return currentTarget;
    }
    
    /**
     * Get current farming state
     */
    public FarmingState getCurrentState() {
        return state;
    }
    
    /**
     * Get the current farming path
     */
    public List<BlockPos> getFarmingPath() {
        return new ArrayList<>(farmingPath);
    }
    
    /**
     * Get farming progress (crops completed / total crops)
     */
    public float getFarmingProgress() {
        if (farmingPath.isEmpty()) return 0.0f;
        return (float) currentPathIndex / farmingPath.size();
    }
    
    /**
     * Check if currently farming
     */
    public boolean isFarming() {
        return state != FarmingState.SCANNING && currentTarget != null;
    }
    
    /**
     * Force scan for new crops
     */
    public void forceScan() {
        state = FarmingState.SCANNING;
        farmingPath.clear();
        currentPathIndex = 0;
        currentTarget = null;
        PathUtils.stopMovement();
    }
    
    /**
     * Get the number of crops in current path
     */
    public int getCropCount() {
        return farmingPath.size();
    }
    
    /**
     * Get the number of crops completed
     */
    public int getCropsCompleted() {
        return currentPathIndex;
    }
}
