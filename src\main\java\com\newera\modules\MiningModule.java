package com.newera.modules;

import com.newera.NewEraMod;
import com.newera.core.ConfigManager;
import com.newera.utils.BlockUtils;
import com.newera.utils.ChatUtils;
import com.newera.utils.InventoryUtils;
import com.newera.utils.PathUtils;
import com.newera.utils.RotationUtils;
import net.minecraft.block.Block;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingSpawnEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

import java.util.List;

/**
 * Mining automation module
 * Handles automatic ore detection, pathfinding, and mining with mob spawn detection
 */
public class MiningModule extends BaseModule {
    
    private final ConfigManager.MiningConfig config;
    
    // Mining state
    private MiningState state = MiningState.SCANNING;
    private BlockPos targetBlock = null;
    private BlockPos accessiblePosition = null;
    private long miningStartTime = 0;
    private float expectedMiningTime = 0;
    private EntityMob spawnedMob = null;
    
    private enum MiningState {
        SCANNING,
        MOVING_TO_BLOCK,
        MINING,
        FIGHTING_SPAWN
    }
    
    public MiningModule(ConfigManager configManager) {
        super(configManager);
        this.config = configManager.getMiningConfig();
    }
    
    @Override
    protected void registerEventHandlers() {
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    @Override
    protected void unregisterEventHandlers() {
        MinecraftForge.EVENT_BUS.unregister(this);
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        state = MiningState.SCANNING;
        targetBlock = null;
        spawnedMob = null;
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        stopMining();
        PathUtils.stopMovement();
    }
    
    @Override
    public void onTick() {
        if (!isInGame()) return;
        
        updateTiming();
        
        // Handle mob spawns first
        if (spawnedMob != null && !spawnedMob.isDead) {
            state = MiningState.FIGHTING_SPAWN;
        }
        
        // Main mining logic
        switch (state) {
            case SCANNING:
                handleScanningState();
                break;
            case MOVING_TO_BLOCK:
                handleMovingState();
                break;
            case MINING:
                handleMiningState();
                break;
            case FIGHTING_SPAWN:
                handleCombatState();
                break;
        }
        
        // Update smooth rotation
        RotationUtils.updateSmoothRotation();
    }
    
    /**
     * Handle scanning for target blocks
     */
    private void handleScanningState() {
        if (!canPerformAction()) return;
        
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);
        
        // Find target blocks
        targetBlock = BlockUtils.findClosestBlock(playerPos, config.range, config.targetBlocks);
        
        if (targetBlock != null) {
            debugLog("Found target block: " + getBlockName(targetBlock) + " at " + targetBlock);
            
            // Find accessible position
            accessiblePosition = PathUtils.findAccessiblePosition(targetBlock, 3);
            
            if (accessiblePosition != null) {
                state = MiningState.MOVING_TO_BLOCK;
                debugLog("Moving to accessible position: " + accessiblePosition);
            } else {
                debugLog("No accessible position found for block");
                targetBlock = null;
            }
        }
        
        updateActionTime();
    }
    
    /**
     * Handle moving to target block
     */
    private void handleMovingState() {
        if (targetBlock == null || accessiblePosition == null) {
            state = MiningState.SCANNING;
            return;
        }
        
        // Check if we've reached the position
        if (PathUtils.hasReachedPosition(accessiblePosition, 2.0)) {
            // Switch to pickaxe
            int pickaxeSlot = config.pickaxeSlot >= 0 ? config.pickaxeSlot : InventoryUtils.findPickaxe();
            if (pickaxeSlot >= 0) {
                InventoryUtils.switchToSlot(pickaxeSlot);
                state = MiningState.MINING;
                debugLog("Reached target, starting to mine");
            } else {
                debugLog("No pickaxe found");
                state = MiningState.SCANNING;
                targetBlock = null;
            }
        } else {
            // Move towards the position
            PathUtils.moveTowards(accessiblePosition);
            
            // Look towards the target
            RotationUtils.smoothRotateTo(
                RotationUtils.getRotationsToBlock(accessiblePosition)[0],
                0 // Keep pitch level for walking
            );
        }
    }
    
    /**
     * Handle mining the target block
     */
    private void handleMiningState() {
        if (targetBlock == null) {
            state = MiningState.SCANNING;
            return;
        }
        
        // Check if block still exists
        if (!BlockUtils.canBreakBlock(targetBlock)) {
            debugLog("Target block no longer exists or cannot be broken");
            state = MiningState.SCANNING;
            targetBlock = null;
            return;
        }
        
        // Look at the block
        RotationUtils.smoothRotateTo(
            RotationUtils.getRotationsToBlock(targetBlock)[0],
            RotationUtils.getRotationsToBlock(targetBlock)[1]
        );
        
        // Start mining if we're looking at it
        if (!RotationUtils.isRotating() && RotationUtils.isLookingAtBlock(targetBlock, 5.0f)) {
            if (miningStartTime == 0) {
                startMining();
            } else {
                continueMining();
            }
        }
    }
    
    /**
     * Handle combat with spawned mobs
     */
    private void handleCombatState() {
        if (spawnedMob == null || spawnedMob.isDead) {
            spawnedMob = null;
            state = MiningState.SCANNING;
            return;
        }
        
        // Switch to weapon
        int weaponSlot = config.weaponSlot >= 0 ? config.weaponSlot : InventoryUtils.findBestWeapon();
        if (weaponSlot >= 0) {
            InventoryUtils.switchToSlot(weaponSlot);
        }
        
        // Look at mob
        RotationUtils.smoothRotateTo(
            RotationUtils.getRotationsToEntity(spawnedMob)[0],
            RotationUtils.getRotationsToEntity(spawnedMob)[1]
        );
        
        // Attack if ready and looking at mob
        if (canAttack() && !RotationUtils.isRotating() && 
            RotationUtils.isLookingAtEntity(spawnedMob, 5.0f)) {
            attackMob();
        }
        
        // Check if mob is too far
        if (getPlayer().getDistanceToEntity(spawnedMob) > 10) {
            spawnedMob = null;
            state = MiningState.SCANNING;
        }
    }
    
    /**
     * Start mining the target block
     */
    private void startMining() {
        if (targetBlock == null) return;
        
        // Calculate expected mining time
        expectedMiningTime = BlockUtils.getMiningTime(targetBlock);
        miningStartTime = System.currentTimeMillis();
        
        // Start digging
        mc.playerController.onPlayerDamageBlock(targetBlock, mc.objectMouseOver.sideHit);
        
        debugLog("Started mining block (expected time: " + expectedMiningTime + "s)");
    }
    
    /**
     * Continue mining the target block
     */
    private void continueMining() {
        if (targetBlock == null) return;
        
        // Continue digging
        mc.playerController.onPlayerDamageBlock(targetBlock, mc.objectMouseOver.sideHit);
        
        // Check if mining should be complete
        long miningTime = System.currentTimeMillis() - miningStartTime;
        if (miningTime > expectedMiningTime * 1000 + 1000) { // Add 1 second buffer
            // Mining should be done, reset and scan for new blocks
            finishMining();
        }
    }
    
    /**
     * Finish mining and reset state
     */
    private void finishMining() {
        debugLog("Finished mining block");
        stopMining();
        state = MiningState.SCANNING;
        targetBlock = null;
        updateActionTime();
    }
    
    /**
     * Stop mining
     */
    private void stopMining() {
        if (miningStartTime > 0) {
            mc.playerController.resetBlockRemoving();
            miningStartTime = 0;
        }
        PathUtils.stopMovement();
    }
    
    /**
     * Attack spawned mob
     */
    private void attackMob() {
        if (spawnedMob == null) return;
        
        mc.playerController.attackEntity(getPlayer(), spawnedMob);
        getPlayer().swingItem();
        updateActionTime();
        
        debugLog("Attacked spawned mob: " + spawnedMob.getName());
    }
    
    /**
     * Check if we can attack (cooldown)
     */
    private boolean canAttack() {
        return canPerformAction();
    }
    
    /**
     * Get block name for logging
     */
    private String getBlockName(BlockPos pos) {
        if (getWorld() == null) return "unknown";
        
        Block block = getWorld().getBlockState(pos).getBlock();
        return Block.blockRegistry.getNameForObject(block).toString();
    }
    
    /**
     * Event handler for mob spawns
     */
    @SubscribeEvent
    public void onMobSpawn(LivingSpawnEvent.SpecialSpawn event) {
        if (!config.mobSpawnDetection || !isEnabled()) return;
        
        if (event.entity instanceof EntityMob) {
            EntityPlayer player = getPlayer();
            if (player != null && player.getDistanceToEntity(event.entity) <= 8) {
                spawnedMob = (EntityMob) event.entity;
                debugLog("Detected mob spawn: " + spawnedMob.getName());
                
                // Send chat message about spawn
                ChatUtils.addMessage("§c[Mining] Mob spawned nearby!");
            }
        }
    }
    
    /**
     * Get current mining target
     */
    public BlockPos getCurrentTarget() {
        return targetBlock;
    }
    
    /**
     * Get current mining state
     */
    public MiningState getCurrentState() {
        return state;
    }
    
    /**
     * Check if currently mining
     */
    public boolean isMining() {
        return state == MiningState.MINING && miningStartTime > 0;
    }
    
    /**
     * Get mining progress (0.0 to 1.0)
     */
    public float getMiningProgress() {
        if (!isMining() || expectedMiningTime <= 0) return 0.0f;
        
        long elapsed = System.currentTimeMillis() - miningStartTime;
        return Math.min(1.0f, elapsed / (expectedMiningTime * 1000));
    }
    
    /**
     * Force scan for new targets
     */
    public void forceScan() {
        state = MiningState.SCANNING;
        targetBlock = null;
        stopMining();
    }
}
