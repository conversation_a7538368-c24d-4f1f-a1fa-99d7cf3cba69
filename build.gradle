buildscript {
    repositories {
        maven { url = "https://repo1.maven.org/maven2/" }
        maven { url = "https://maven.minecraftforge.net/" }
        maven { url = "https://files.minecraftforge.net/maven" }
        jcenter()
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:1.2.2'
    }
}

apply plugin: 'net.minecraftforge.gradle.forge'

version = "1.0.0"
group = "com.newera"
archivesBaseName = "NewEra"

minecraft {
    version = "1.8-11.14.3.1450-1.8"
    runDir = "eclipse"

    mappings = "snapshot_20141001"
}

repositories {
    maven { url = "https://repo1.maven.org/maven2/" }
    maven { url = "https://maven.minecraftforge.net/" }
    maven { url = "https://files.minecraftforge.net/maven" }
    maven { url = "https://libraries.minecraft.net/" }
}

dependencies {
    // No additional dependencies needed for basic functionality
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }

    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}

jar {
    manifest {
        attributes 'FMLCorePlugin': 'com.newera.NewEraMod'
    }
}