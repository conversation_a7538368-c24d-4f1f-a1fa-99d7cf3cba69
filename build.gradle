buildscript {
    repositories {
        maven { url = "https://repo1.maven.org/maven2/" }
        maven { url = "https://maven.minecraftforge.net/" }
        maven { url = "https://files.minecraftforge.net/maven" }
        gradlePluginPortal()
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:2.1-SNAPSHOT'
    }
}

apply plugin: 'forge'

version = "1.0.0"
group = "com.newera"
archivesBaseName = "NewEra"

minecraft {
    version = "1.8.9-11.15.1.2318-1.8.9"
    runDir = "run"

    mappings = "stable_22"
}

repositories {
    maven { url = "https://repo1.maven.org/maven2/" }
    maven { url = "https://maven.minecraftforge.net/" }
    maven { url = "https://files.minecraftforge.net/maven" }
    maven { url = "https://libraries.minecraft.net/" }
}

dependencies {
    // No additional dependencies needed for basic functionality
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }

    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}

jar {
    manifest {
        attributes 'FMLCorePlugin': 'com.newera.NewEraMod'
    }
}