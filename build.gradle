buildscript {
    repositories {
        jcenter()
        maven { url = "http://files.minecraftforge.net/maven" }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:1.2-SNAPSHOT'
    }
}

apply plugin: 'forge'

version = "1.0.0"
group = "com.newera"
archivesBaseName = "NewEra"

minecraft {
    version = "1.8-11.14.4.1563-1.8"
    runDir = "eclipse"

    mappings = "snapshot_20141001"
}

dependencies {
    // No additional dependencies needed for basic functionality
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }
        
    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}

jar {
    manifest {
        attributes 'FMLCorePlugin': 'com.newera.NewEraMod'
    }
}
