package com.newera.core;

import com.newera.NewEraMod;
import com.newera.gui.GuiNewEra;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import org.lwjgl.input.Keyboard;

/**
 * Key binding manager for NewEra mod
 * Handles registration and processing of all keybinds
 */
public class KeyBindings {
    
    // Key binding instances
    private KeyBinding openGui;
    private KeyBinding toggleFishing;
    private KeyBinding toggleCombat;
    private KeyBinding toggleMining;
    private KeyBinding toggleFarming;
    private KeyBinding toggleLearning;
    private KeyBinding toggleDebug;
    
    // Key binding category
    private static final String CATEGORY = "NewEra";
    
    public KeyBindings() {
        registerKeyBindings();
    }
    
    /**
     * Register all key bindings with Minecraft
     */
    private void registerKeyBindings() {
        // Initialize key bindings
        openGui = new KeyBinding("key.newera.gui", Keyboard.KEY_R, CATEGORY);
        toggleFishing = new KeyBinding("key.newera.fishing", Keyboard.KEY_F, CATEGORY);
        toggleCombat = new KeyBinding("key.newera.combat", Keyboard.KEY_G, CATEGORY);
        toggleMining = new KeyBinding("key.newera.mining", Keyboard.KEY_H, CATEGORY);
        toggleFarming = new KeyBinding("key.newera.farming", Keyboard.KEY_J, CATEGORY);
        toggleLearning = new KeyBinding("key.newera.learning", Keyboard.KEY_L, CATEGORY);
        toggleDebug = new KeyBinding("key.newera.debug", Keyboard.KEY_P, CATEGORY);
        
        // Register with Minecraft
        ClientRegistry.registerKeyBinding(openGui);
        ClientRegistry.registerKeyBinding(toggleFishing);
        ClientRegistry.registerKeyBinding(toggleCombat);
        ClientRegistry.registerKeyBinding(toggleMining);
        ClientRegistry.registerKeyBinding(toggleFarming);
        ClientRegistry.registerKeyBinding(toggleLearning);
        ClientRegistry.registerKeyBinding(toggleDebug);
    }
    
    /**
     * Handle key input events
     */
    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        // Only process in-game
        if (Minecraft.getMinecraft().currentScreen != null) {
            return;
        }
        
        ModuleManager moduleManager = NewEraMod.getModuleManager();
        if (moduleManager == null) {
            return;
        }
        
        // Open GUI
        if (openGui.isPressed()) {
            Minecraft.getMinecraft().displayGuiScreen(new GuiNewEra());
        }
        
        // Toggle modules
        if (toggleFishing.isPressed()) {
            moduleManager.toggleModule("fishing");
        }
        
        if (toggleCombat.isPressed()) {
            moduleManager.toggleModule("combat");
        }
        
        if (toggleMining.isPressed()) {
            moduleManager.toggleModule("mining");
        }
        
        if (toggleFarming.isPressed()) {
            moduleManager.toggleModule("farming");
        }
        
        if (toggleLearning.isPressed()) {
            moduleManager.toggleModule("learning");
        }
        
        // Toggle debug mode
        if (toggleDebug.isPressed()) {
            ConfigManager.GeneralConfig config = NewEraMod.getConfigManager().getGeneralConfig();
            config.debug = !config.debug;
            NewEraMod.getConfigManager().saveConfig();
            
            String status = config.debug ? "enabled" : "disabled";
            NewEraMod.info("Debug mode " + status);
        }
    }
    
    // Getter methods for key bindings
    public KeyBinding getOpenGui() { return openGui; }
    public KeyBinding getToggleFishing() { return toggleFishing; }
    public KeyBinding getToggleCombat() { return toggleCombat; }
    public KeyBinding getToggleMining() { return toggleMining; }
    public KeyBinding getToggleFarming() { return toggleFarming; }
    public KeyBinding getToggleLearning() { return toggleLearning; }
    public KeyBinding getToggleDebug() { return toggleDebug; }
}
