package com.newera.core;

import com.newera.NewEraMod;
import com.newera.modules.*;
import com.newera.utils.ChatUtils;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.HashMap;
import java.util.Map;

/**
 * Module manager for NewEra mod
 * Handles registration, enabling/disabling, and coordination of all modules
 */
public class ModuleManager {
    
    private final ConfigManager configManager;
    private final Map<String, BaseModule> modules;
    
    // Module instances
    private FishingModule fishingModule;
    private CombatModule combatModule;
    private MiningModule miningModule;
    private FarmingModule farmingModule;
    private LearningModule learningModule;
    
    public ModuleManager(ConfigManager configManager) {
        this.configManager = configManager;
        this.modules = new HashMap<>();
        
        initializeModules();
    }
    
    /**
     * Initialize all modules
     */
    private void initializeModules() {
        // Create module instances
        fishingModule = new FishingModule(configManager);
        combatModule = new CombatModule(configManager);
        miningModule = new MiningModule(configManager);
        farmingModule = new FarmingModule(configManager);
        learningModule = new LearningModule(configManager, this);
        
        // Register modules
        modules.put("fishing", fishingModule);
        modules.put("combat", combatModule);
        modules.put("mining", miningModule);
        modules.put("farming", farmingModule);
        modules.put("learning", learningModule);
        
        NewEraMod.info("Initialized {} modules", modules.size());
        
        // Enable modules based on configuration
        for (Map.Entry<String, BaseModule> entry : modules.entrySet()) {
            if (isModuleEnabledInConfig(entry.getKey())) {
                enableModule(entry.getKey());
            }
        }
    }
    
    /**
     * Check if module is enabled in configuration
     */
    private boolean isModuleEnabledInConfig(String moduleName) {
        switch (moduleName) {
            case "fishing": return configManager.getFishingConfig().enabled;
            case "combat": return configManager.getCombatConfig().enabled;
            case "mining": return configManager.getMiningConfig().enabled;
            case "farming": return configManager.getFarmingConfig().enabled;
            case "learning": return configManager.getLearningConfig().enabled;
            default: return false;
        }
    }
    
    /**
     * Enable a module by name
     */
    public void enableModule(String moduleName) {
        BaseModule module = modules.get(moduleName);
        if (module != null && !module.isEnabled()) {
            try {
                module.onEnable();
                updateConfigForModule(moduleName, true);
                
                if (configManager.getGeneralConfig().chatNotifications) {
                    ChatUtils.addMessage("§6[NewEra] §a" + getModuleDisplayName(moduleName) + " enabled");
                }
                
                NewEraMod.debugLog("Module " + moduleName + " enabled");
            } catch (Exception e) {
                NewEraMod.error("Failed to enable module " + moduleName, e);
            }
        }
    }
    
    /**
     * Disable a module by name
     */
    public void disableModule(String moduleName) {
        BaseModule module = modules.get(moduleName);
        if (module != null && module.isEnabled()) {
            try {
                module.onDisable();
                updateConfigForModule(moduleName, false);
                
                if (configManager.getGeneralConfig().chatNotifications) {
                    ChatUtils.addMessage("§6[NewEra] §c" + getModuleDisplayName(moduleName) + " disabled");
                }
                
                NewEraMod.debugLog("Module " + moduleName + " disabled");
            } catch (Exception e) {
                NewEraMod.error("Failed to disable module " + moduleName, e);
            }
        }
    }
    
    /**
     * Toggle a module by name
     */
    public void toggleModule(String moduleName) {
        BaseModule module = modules.get(moduleName);
        if (module != null) {
            if (module.isEnabled()) {
                disableModule(moduleName);
            } else {
                enableModule(moduleName);
            }
        }
    }
    
    /**
     * Update configuration for module enable/disable state
     */
    private void updateConfigForModule(String moduleName, boolean enabled) {
        switch (moduleName) {
            case "fishing":
                configManager.getFishingConfig().enabled = enabled;
                break;
            case "combat":
                configManager.getCombatConfig().enabled = enabled;
                break;
            case "mining":
                configManager.getMiningConfig().enabled = enabled;
                break;
            case "farming":
                configManager.getFarmingConfig().enabled = enabled;
                break;
            case "learning":
                configManager.getLearningConfig().enabled = enabled;
                break;
        }
        configManager.saveConfig();
    }
    
    /**
     * Get display name for module
     */
    private String getModuleDisplayName(String moduleName) {
        switch (moduleName) {
            case "fishing": return "Fishing Module";
            case "combat": return "Combat Module";
            case "mining": return "Mining Module";
            case "farming": return "Farming Module";
            case "learning": return "Learning Module";
            default: return moduleName;
        }
    }
    
    /**
     * Main tick handler - called every client tick
     */
    public void onTick() {
        for (BaseModule module : modules.values()) {
            if (module.isEnabled()) {
                try {
                    module.onTick();
                } catch (Exception e) {
                    NewEraMod.error("Error in module " + module.getClass().getSimpleName() + " tick", e);
                    // Disable module on critical error
                    module.onDisable();
                }
            }
        }
    }
    
    /**
     * Get module by name
     */
    public BaseModule getModule(String moduleName) {
        return modules.get(moduleName);
    }
    
    /**
     * Check if module is enabled
     */
    public boolean isModuleEnabled(String moduleName) {
        BaseModule module = modules.get(moduleName);
        return module != null && module.isEnabled();
    }
    
    /**
     * Get all modules
     */
    public Map<String, BaseModule> getAllModules() {
        return new HashMap<>(modules);
    }
    
    // Getter methods for specific modules
    public FishingModule getFishingModule() { return fishingModule; }
    public CombatModule getCombatModule() { return combatModule; }
    public MiningModule getMiningModule() { return miningModule; }
    public FarmingModule getFarmingModule() { return farmingModule; }
    public LearningModule getLearningModule() { return learningModule; }
    
    /**
     * Disable all modules (for cleanup)
     */
    public void disableAllModules() {
        for (String moduleName : modules.keySet()) {
            disableModule(moduleName);
        }
    }
}
