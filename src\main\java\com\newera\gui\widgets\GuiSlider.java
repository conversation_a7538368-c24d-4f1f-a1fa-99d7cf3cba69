package com.newera.gui.widgets;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.util.MathHelper;

/**
 * Custom slider widget for the NewEra GUI
 */
public class GuiSlider extends GuiButton {
    
    private float value;
    private final float minValue;
    private final float maxValue;
    private final String prefix;
    private final String suffix;
    private boolean dragging = false;
    
    public GuiSlider(int id, int x, int y, int width, int height, 
                     String prefix, String suffix, 
                     float minValue, float maxValue, float currentValue) {
        super(id, x, y, width, height, "");
        this.prefix = prefix;
        this.suffix = suffix;
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.value = MathHelper.clamp_float(currentValue, minValue, maxValue);
        updateDisplayString();
    }
    
    /**
     * Update the display string based on current value
     */
    private void updateDisplayString() {
        if (maxValue - minValue < 1.0f) {
            // Show decimal places for small ranges
            this.displayString = prefix + String.format("%.2f", value) + suffix;
        } else {
            // Show integer for large ranges
            this.displayString = prefix + String.format("%.0f", value) + suffix;
        }
    }
    
    /**
     * Handle mouse click
     */
    @Override
    public boolean mousePressed(Minecraft mc, int mouseX, int mouseY) {
        if (this.enabled && this.visible && mouseX >= this.xPosition && 
            mouseY >= this.yPosition && mouseX < this.xPosition + this.width && 
            mouseY < this.yPosition + this.height) {
            
            this.dragging = true;
            updateValueFromMouse(mouseX);
            return true;
        }
        return false;
    }
    
    /**
     * Handle mouse release
     */
    public void mouseReleased(int mouseX, int mouseY) {
        this.dragging = false;
    }
    
    /**
     * Handle mouse drag
     */
    public void mouseDragged(Minecraft mc, int mouseX, int mouseY) {
        if (this.dragging) {
            updateValueFromMouse(mouseX);
        }
    }
    
    /**
     * Update value based on mouse position
     */
    private void updateValueFromMouse(int mouseX) {
        float relativeX = (float)(mouseX - this.xPosition) / (float)this.width;
        relativeX = MathHelper.clamp_float(relativeX, 0.0f, 1.0f);
        
        this.value = minValue + (maxValue - minValue) * relativeX;
        updateDisplayString();
    }
    
    /**
     * Draw the slider
     */
    @Override
    public void drawButton(Minecraft mc, int mouseX, int mouseY) {
        if (!this.visible) return;
        
        mc.getTextureManager().bindTexture(buttonTextures);
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        
        boolean hovered = mouseX >= this.xPosition && mouseY >= this.yPosition && 
                         mouseX < this.xPosition + this.width && mouseY < this.yPosition + this.height;
        
        int textureY = this.getHoverState(hovered);
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(770, 771, 1, 0);
        GlStateManager.blendFunc(770, 771);
        
        // Draw button background
        this.drawTexturedModalRect(this.xPosition, this.yPosition, 0, 46 + textureY * 20, this.width / 2, this.height);
        this.drawTexturedModalRect(this.xPosition + this.width / 2, this.yPosition, 200 - this.width / 2, 46 + textureY * 20, this.width / 2, this.height);
        
        // Draw slider handle
        float handlePos = (value - minValue) / (maxValue - minValue);
        int handleX = (int)(this.xPosition + handlePos * (this.width - 8));
        this.drawTexturedModalRect(handleX, this.yPosition, 0, 66, 4, 20);
        this.drawTexturedModalRect(handleX + 4, this.yPosition, 196, 66, 4, 20);
        
        // Draw text
        int textColor = 14737632;
        if (!this.enabled) {
            textColor = 10526880;
        } else if (hovered) {
            textColor = 16777120;
        }
        
        this.drawCenteredString(mc.fontRendererObj, this.displayString, 
                               this.xPosition + this.width / 2, 
                               this.yPosition + (this.height - 8) / 2, textColor);
    }
    
    /**
     * Get the current value
     */
    public float getValue() {
        return value;
    }
    
    /**
     * Set the value
     */
    public void setValue(float value) {
        this.value = MathHelper.clamp_float(value, minValue, maxValue);
        updateDisplayString();
    }
    
    /**
     * Get the minimum value
     */
    public float getMinValue() {
        return minValue;
    }
    
    /**
     * Get the maximum value
     */
    public float getMaxValue() {
        return maxValue;
    }
    
    /**
     * Check if the slider is being dragged
     */
    public boolean isDragging() {
        return dragging;
    }
}
