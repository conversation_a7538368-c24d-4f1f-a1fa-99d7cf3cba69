package com.newera.utils;

import net.minecraft.block.Block;
import net.minecraft.block.BlockCrops;
import net.minecraft.block.state.IBlockState;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.Blocks;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MovingObjectPosition;
import net.minecraft.util.Vec3;
import net.minecraft.world.World;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for block-related operations
 */
public class BlockUtils {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    /**
     * Get all blocks within a specified range
     */
    public static List<BlockPos> getBlocksInRange(BlockPos center, int range) {
        List<BlockPos> blocks = new ArrayList<>();
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = center.add(x, y, z);
                    blocks.add(pos);
                }
            }
        }
        
        return blocks;
    }
    
    /**
     * Get blocks of specific types within range
     */
    public static List<BlockPos> getBlocksOfType(BlockPos center, int range, List<String> blockNames) {
        List<BlockPos> blocks = new ArrayList<>();
        World world = mc.theWorld;
        if (world == null) return blocks;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();
                    String blockName = Block.blockRegistry.getNameForObject(block).toString();
                    
                    if (blockNames.contains(blockName)) {
                        blocks.add(pos);
                    }
                }
            }
        }
        
        return blocks;
    }
    
    /**
     * Find the closest block of a specific type
     */
    public static BlockPos findClosestBlock(BlockPos center, int range, List<String> blockNames) {
        World world = mc.theWorld;
        if (world == null) return null;
        
        BlockPos closest = null;
        double closestDistance = Double.MAX_VALUE;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();
                    String blockName = Block.blockRegistry.getNameForObject(block).toString();
                    
                    if (blockNames.contains(blockName)) {
                        double distance = center.distanceSq(pos);
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            closest = pos;
                        }
                    }
                }
            }
        }
        
        return closest;
    }
    
    /**
     * Check if a block can be broken
     */
    public static boolean canBreakBlock(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        IBlockState state = world.getBlockState(pos);
        Block block = state.getBlock();
        
        // Check if block is air or unbreakable
        if (block == Blocks.air || block.getBlockHardness(world, pos) < 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if a crop is fully grown
     */
    public static boolean isCropFullyGrown(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        IBlockState state = world.getBlockState(pos);
        Block block = state.getBlock();
        
        if (block instanceof BlockCrops) {
            BlockCrops crop = (BlockCrops) block;
            int age = crop.getAge(state);
            int maxAge = crop.getMaxAge();
            return age >= maxAge;
        }
        
        return false;
    }
    
    /**
     * Get all fully grown crops within range
     */
    public static List<BlockPos> getFullyGrownCrops(BlockPos center, int range, List<String> cropNames) {
        List<BlockPos> crops = new ArrayList<>();
        World world = mc.theWorld;
        if (world == null) return crops;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = center.add(x, y, z);
                    Block block = world.getBlockState(pos).getBlock();
                    String blockName = Block.blockRegistry.getNameForObject(block).toString();
                    
                    if (cropNames.contains(blockName) && isCropFullyGrown(pos)) {
                        crops.add(pos);
                    }
                }
            }
        }
        
        return crops;
    }
    
    /**
     * Check if player can reach a block
     */
    public static boolean canReachBlock(BlockPos pos) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        double distance = player.getDistanceSq(pos);
        return distance <= 36; // 6 blocks reach distance squared
    }
    
    /**
     * Check line of sight to a block
     */
    public static boolean hasLineOfSight(BlockPos pos) {
        EntityPlayer player = mc.thePlayer;
        World world = mc.theWorld;
        if (player == null || world == null) return false;
        
        Vec3 playerPos = player.getPositionEyes(1.0f);
        Vec3 blockPos = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        
        MovingObjectPosition result = world.rayTraceBlocks(playerPos, blockPos, false, true, false);
        
        return result == null || result.getBlockPos().equals(pos);
    }
    
    /**
     * Get the block the player is looking at
     */
    public static BlockPos getLookingAtBlock() {
        MovingObjectPosition result = mc.objectMouseOver;
        if (result != null && result.typeOfHit == MovingObjectPosition.MovingObjectType.BLOCK) {
            return result.getBlockPos();
        }
        return null;
    }
    
    /**
     * Check if a position is safe to stand on
     */
    public static boolean isSafePosition(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        // Check if the block below is solid
        BlockPos below = pos.down();
        IBlockState belowState = world.getBlockState(below);
        if (!belowState.getBlock().isFullBlock()) {
            return false;
        }
        
        // Check if the position and above are clear
        if (!world.getBlockState(pos).getBlock().isPassable(world, pos)) {
            return false;
        }
        
        if (!world.getBlockState(pos.up()).getBlock().isPassable(world, pos.up())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get the hardness of a block
     */
    public static float getBlockHardness(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return 0;
        
        IBlockState state = world.getBlockState(pos);
        return state.getBlock().getBlockHardness(world, pos);
    }
    
    /**
     * Calculate mining time for a block
     */
    public static float getMiningTime(BlockPos pos) {
        EntityPlayer player = mc.thePlayer;
        World world = mc.theWorld;
        if (player == null || world == null) return Float.MAX_VALUE;
        
        IBlockState state = world.getBlockState(pos);
        Block block = state.getBlock();
        
        float hardness = block.getBlockHardness(world, pos);
        if (hardness < 0) return Float.MAX_VALUE;
        
        float speed = 1.0f;
        if (player.inventory.getCurrentItem() != null) {
            speed = player.inventory.getCurrentItem().getStrVsBlock(block);
        }
        
        return hardness / speed;
    }
    
    /**
     * Check if a block is water
     */
    public static boolean isWater(BlockPos pos) {
        World world = mc.theWorld;
        if (world == null) return false;
        
        Block block = world.getBlockState(pos).getBlock();
        return block == Blocks.water || block == Blocks.flowing_water;
    }
    
    /**
     * Find water blocks within range
     */
    public static List<BlockPos> findWaterBlocks(BlockPos center, int range) {
        List<BlockPos> waterBlocks = new ArrayList<>();
        World world = mc.theWorld;
        if (world == null) return waterBlocks;
        
        for (int x = -range; x <= range; x++) {
            for (int y = -range; y <= range; y++) {
                for (int z = -range; z <= range; z++) {
                    BlockPos pos = center.add(x, y, z);
                    if (isWater(pos)) {
                        waterBlocks.add(pos);
                    }
                }
            }
        }
        
        return waterBlocks;
    }
}
