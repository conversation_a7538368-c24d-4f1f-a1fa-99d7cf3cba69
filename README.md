# NewEra Mod - Minecraft 1.8.9 Automation Client

Um mod cliente para Minecraft Forge 1.8.9 que automatiza pesca, combate, mineração e farming com sistema de aprendizagem inteligente.

## Funcionalidades

- **Fishing Module**: Pesca automática com detecção inteligente de mordidas
- **Combat Module**: Combate automático com seleção de alvos e rotação suave
- **Mining Module**: Mineração automática com pathfinding e detecção de spawns
- **Farming Module**: Colheita automática com suporte a múltiplas culturas
- **Learning Module**: Aprende padrões do jogador e otimiza parâmetros automaticamente

## Requisitos

- Java 8 (JDK 1.8)
- Minecraft 1.8.9
- Minecraft Forge 1.8.9-11.15.1.2318-1.8.9
- Gradle (incluído no wrapper)

## Instalação do Ambiente de Desenvolvimento

### 1. Instalar Java 8
- Baixe e instale o JDK 8 da Oracle ou OpenJDK
- Configure JAVA_HOME para apontar para a instalação do Java 8

### 2. Baixar Forge MDK 1.8.9
```bash
# Clone ou baixe este repositório
git clone <repository-url>
cd NewEra
```

### 3. Configurar o Projeto
```bash
# No Windows
gradlew setupDecompWorkspace
gradlew setupDevWorkspace

# No Linux/Mac
./gradlew setupDecompWorkspace
./gradlew setupDevWorkspace
```

### 4. Compilar o Mod
```bash
# No Windows
gradlew build

# No Linux/Mac
./gradlew build
```

O arquivo .jar será gerado em `build/libs/NewEra-1.0.0.jar`

## Instalação do Mod

1. Instale Minecraft Forge 1.8.9
2. Copie o arquivo `NewEra-1.0.0.jar` para a pasta `mods` do Minecraft
3. Inicie o Minecraft com o perfil Forge

## Configuração e Uso

### Keybinds Padrão
- **R**: Abrir GUI principal
- **F**: Toggle Fishing Module
- **G**: Toggle Combat Module
- **H**: Toggle Mining Module
- **J**: Toggle Farming Module
- **L**: Toggle Learning Module
- **P**: Toggle Debug Mode

### GUI Principal
Acesse a GUI pressionando 'R' para:
- Ativar/desativar módulos individualmente
- Ajustar parâmetros com sliders
- Configurar listas de blocos/mobs
- Visualizar estatísticas de aprendizagem

### Configuração Inicial

1. **Fishing**: Selecione vara de pesca e arma na GUI
2. **Combat**: Configure lista de mobs alvo e raio de ataque
3. **Mining**: Defina blocos alvo e picareta preferida
4. **Farming**: Configure tipos de cultura e layout da farm
5. **Learning**: Ative para otimização automática

## Testes Manuais / QA

### Teste do Fishing Module
1. Equipe uma vara de pesca
2. Ative o módulo (F ou GUI)
3. Posicione-se próximo à água
4. Verifique se lança automaticamente e detecta mordidas

### Teste do Combat Module
1. Configure mobs alvo na GUI
2. Ative o módulo (G ou GUI)
3. Aproxime-se de mobs hostis
4. Verifique rotação suave e ataques automáticos

### Teste do Mining Module
1. Configure blocos alvo (ex: diamante, ferro)
2. Equipe picareta adequada
3. Ative o módulo (H ou GUI)
4. Verifique detecção e mineração automática

### Teste do Farming Module
1. Posicione-se em uma farm
2. Configure culturas na GUI
3. Ative o módulo (J ou GUI)
4. Verifique colheita automática e pathfinding

### Teste do Learning Module
1. Ative o módulo (L ou GUI)
2. Use outros módulos normalmente
3. Verifique logs de aprendizagem no debug
4. Observe otimizações automáticas de parâmetros

## Configuração Avançada

O arquivo de configuração é salvo em `.minecraft/config/newera.cfg` e permite:
- Ajuste fino de todos os parâmetros
- Listas personalizadas de blocos/mobs
- Configurações de anti-cheat
- Parâmetros de aprendizagem

## Desenvolvimento e Extensão

### Adicionar Novos Módulos
1. Estenda `BaseModule` em `modules/`
2. Implemente `onEnable()`, `onDisable()`, `onTick()`
3. Registre no `ModuleManager`

### Adicionar Novas Funcionalidades de Aprendizagem
1. Edite `LearningModule.java`
2. Adicione novos padrões em `analyzePlayerBehavior()`
3. Implemente otimizações em `optimizeParameters()`

### Estrutura do Código
- `core/`: Gerenciamento central e configurações
- `modules/`: Módulos de automação
- `utils/`: Utilitários compartilhados
- `gui/`: Interface gráfica

## Licença

MIT License - veja LICENSE para detalhes.

## Troubleshooting

### Problemas Comuns

1. **Mod não carrega**: Verifique se está usando Java 8 e Forge 1.8.9
2. **GUI não abre**: Verifique se a tecla R não está conflitando com outros mods
3. **Módulos não funcionam**: Certifique-se de que estão ativados na GUI
4. **Performance baixa**: Reduza os ranges dos módulos na configuração

### Logs de Debug

Ative o modo debug na GUI ou configuração para ver logs detalhados:
- Logs aparecem no console do Minecraft
- Mensagens de debug aparecem no chat do jogo
- Verifique o arquivo `logs/latest.log` para erros

## Compatibilidade

- **Minecraft**: 1.8.9 apenas
- **Forge**: 11.15.1.2318-1.8.9 ou superior
- **Java**: 8 (JDK 1.8)
- **Outros mods**: Compatível com a maioria dos mods cliente

## Contribuição

Para contribuir com o projeto:
1. Fork o repositório
2. Crie uma branch para sua feature
3. Faça commit das mudanças
4. Abra um Pull Request

## Aviso Legal

Este mod é apenas para uso educacional e em servidores que permitem automação.
- Verifique as regras do servidor antes de usar
- Use por sua própria conta e risco
- Os desenvolvedores não se responsabilizam por bans ou punições
- Respeite os termos de serviço do Minecraft
