package com.newera;

import com.newera.core.ConfigManager;
import com.newera.core.KeyBindings;
import com.newera.core.ModuleManager;
import com.newera.utils.ChatUtils;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * NewEra Mod - Advanced automation client for Minecraft 1.8.9
 * 
 * Main mod class that handles initialization and core event handling.
 * This mod provides automated fishing, combat, mining, farming, and learning capabilities.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mod(modid = NewEraMod.MODID, version = NewEraMod.VERSION, clientSideOnly = true)
public class NewEraMod {
    
    public static final String MODID = "newera";
    public static final String VERSION = "1.0.0";
    public static final String NAME = "NewEra";
    
    public static final Logger LOGGER = LogManager.getLogger(MODID);
    
    @Mod.Instance(MODID)
    public static NewEraMod instance;
    
    private ModuleManager moduleManager;
    private ConfigManager configManager;
    private KeyBindings keyBindings;
    
    /**
     * Pre-initialization phase - setup configuration and logging
     */
    @Mod.EventHandler
    public void preInit(FMLPreInitializationEvent event) {
        LOGGER.info("NewEra Mod v{} - Pre-initialization started", VERSION);
        
        // Initialize configuration manager
        configManager = new ConfigManager(event.getSuggestedConfigurationFile());
        
        LOGGER.info("Configuration loaded successfully");
    }
    
    /**
     * Initialization phase - setup modules, keybindings, and event handlers
     */
    @Mod.EventHandler
    public void init(FMLInitializationEvent event) {
        LOGGER.info("NewEra Mod v{} - Initialization started", VERSION);
        
        // Initialize key bindings
        keyBindings = new KeyBindings();
        
        // Initialize module manager
        moduleManager = new ModuleManager(configManager);
        
        // Register event handlers
        MinecraftForge.EVENT_BUS.register(this);
        MinecraftForge.EVENT_BUS.register(keyBindings);
        MinecraftForge.EVENT_BUS.register(moduleManager);
        
        LOGGER.info("NewEra Mod initialized successfully");
        
        // Welcome message
        if (configManager.getGeneralConfig().chatNotifications) {
            ChatUtils.addDelayedMessage("§6[NewEra] §fMod loaded! Press §eR §fto open GUI", 3000);
        }
    }
    
    /**
     * Main tick event handler - delegates to module manager
     */
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase == TickEvent.Phase.START && moduleManager != null) {
            moduleManager.onTick();
            // Process delayed chat messages
            ChatUtils.processDelayedMessages();
        }
    }
    
    /**
     * Get the module manager instance
     * @return ModuleManager instance
     */
    public static ModuleManager getModuleManager() {
        return instance.moduleManager;
    }
    
    /**
     * Get the configuration manager instance
     * @return ConfigManager instance
     */
    public static ConfigManager getConfigManager() {
        return instance.configManager;
    }
    
    /**
     * Get the key bindings instance
     * @return KeyBindings instance
     */
    public static KeyBindings getKeyBindings() {
        return instance.keyBindings;
    }
    
    /**
     * Check if debug mode is enabled
     * @return true if debug mode is active
     */
    public static boolean isDebugMode() {
        return instance.configManager != null && instance.configManager.getGeneralConfig().debug;
    }
    
    /**
     * Log debug message if debug mode is enabled
     * @param message Debug message to log
     */
    public static void debugLog(String message) {
        if (isDebugMode()) {
            LOGGER.info("[DEBUG] {}", message);
            ChatUtils.addMessage("§7[DEBUG] §f" + message);
        }
    }
    
    /**
     * Log info message
     * @param message Info message to log
     */
    public static void info(String message) {
        LOGGER.info(message);
    }
    
    /**
     * Log warning message
     * @param message Warning message to log
     */
    public static void warn(String message) {
        LOGGER.warn(message);
    }
    
    /**
     * Log error message
     * @param message Error message to log
     */
    public static void error(String message) {
        LOGGER.error(message);
    }
    
    /**
     * Log error message with exception
     * @param message Error message to log
     * @param throwable Exception to log
     */
    public static void error(String message, Throwable throwable) {
        LOGGER.error(message, throwable);
    }
}
