package com.newera.utils;

import net.minecraft.client.Minecraft;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.IChatComponent;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Utility class for chat-related operations
 */
public class ChatUtils {
    
    private static final List<DelayedMessage> delayedMessages = new ArrayList<>();
    
    /**
     * Add a message to the chat
     */
    public static void addMessage(String message) {
        if (Minecraft.getMinecraft().thePlayer != null) {
            IChatComponent chatComponent = new ChatComponentText(message);
            Minecraft.getMinecraft().thePlayer.addChatMessage(chatComponent);
        }
    }
    
    /**
     * Add a message to the chat with formatting
     */
    public static void addMessage(IChatComponent message) {
        if (Minecraft.getMinecraft().thePlayer != null) {
            Minecraft.getMinecraft().thePlayer.addChatMessage(message);
        }
    }
    
    /**
     * Add a delayed message that will be sent after a specified time
     */
    public static void addDelayedMessage(String message, long delayMs) {
        delayedMessages.add(new DelayedMessage(message, System.currentTimeMillis() + delayMs));
    }
    
    /**
     * Process delayed messages (should be called from a tick handler)
     */
    public static void processDelayedMessages() {
        if (delayedMessages.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        Iterator<DelayedMessage> iterator = delayedMessages.iterator();

        while (iterator.hasNext()) {
            DelayedMessage delayedMessage = iterator.next();
            if (currentTime >= delayedMessage.sendTime) {
                addMessage(delayedMessage.message);
                iterator.remove();
            }
        }
    }

    /**
     * Initialize delayed message processing (call from main mod)
     */
    public static void init() {
        // This method can be called from the main mod to ensure proper initialization
    }
    
    /**
     * Send a message with prefix
     */
    public static void sendPrefixedMessage(String prefix, String message) {
        addMessage("§6[" + prefix + "] §f" + message);
    }
    
    /**
     * Send a success message
     */
    public static void sendSuccessMessage(String message) {
        addMessage("§a✓ " + message);
    }
    
    /**
     * Send an error message
     */
    public static void sendErrorMessage(String message) {
        addMessage("§c✗ " + message);
    }
    
    /**
     * Send a warning message
     */
    public static void sendWarningMessage(String message) {
        addMessage("§e⚠ " + message);
    }
    
    /**
     * Send an info message
     */
    public static void sendInfoMessage(String message) {
        addMessage("§b🛈 " + message);
    }
    
    /**
     * Clear chat (client-side only)
     */
    public static void clearChat() {
        if (Minecraft.getMinecraft().ingameGUI != null) {
            Minecraft.getMinecraft().ingameGUI.getChatGUI().clearChatMessages();
        }
    }
    
    /**
     * Check if a chat message contains specific text
     */
    public static boolean messageContains(String message, String... keywords) {
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        for (String keyword : keywords) {
            if (lowerMessage.contains(keyword.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Strip color codes from a message
     */
    public static String stripColorCodes(String message) {
        if (message == null) {
            return null;
        }
        return message.replaceAll("§[0-9a-fk-or]", "");
    }
    
    /**
     * Format a message with color codes
     */
    public static String formatMessage(String message, String colorCode) {
        return colorCode + message + "§r";
    }
    
    /**
     * Create a clickable chat component
     */
    public static IChatComponent createClickableMessage(String message, String command) {
        ChatComponentText component = new ChatComponentText(message);
        // Note: In 1.8.9, click events would need additional setup
        // This is a simplified version
        return component;
    }
    
    /**
     * Helper class for delayed messages
     */
    private static class DelayedMessage {
        final String message;
        final long sendTime;
        
        DelayedMessage(String message, long sendTime) {
            this.message = message;
            this.sendTime = sendTime;
        }
    }
}
