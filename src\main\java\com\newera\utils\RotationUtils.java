package com.newera.utils;

import net.minecraft.client.Minecraft;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.util.Vec3;

/**
 * Utility class for rotation and looking calculations
 */
public class RotationUtils {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    // Smooth rotation variables
    private static float targetYaw = 0;
    private static float targetPitch = 0;
    private static boolean isRotating = false;
    private static final float ROTATION_SPEED = 10.0f; // degrees per tick
    
    /**
     * Calculate the yaw and pitch needed to look at a position
     */
    public static float[] getRotationsToPosition(Vec3 position) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return new float[]{0, 0};
        
        Vec3 playerPos = player.getPositionEyes(1.0f);
        
        double deltaX = position.xCoord - playerPos.xCoord;
        double deltaY = position.yCoord - playerPos.yCoord;
        double deltaZ = position.zCoord - playerPos.zCoord;
        
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) -(Math.atan2(deltaY, distance) * 180.0 / Math.PI);
        
        return new float[]{yaw, pitch};
    }
    
    /**
     * Calculate rotations to look at a block position
     */
    public static float[] getRotationsToBlock(BlockPos pos) {
        Vec3 blockCenter = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        return getRotationsToPosition(blockCenter);
    }
    
    /**
     * Calculate rotations to look at an entity
     */
    public static float[] getRotationsToEntity(Entity entity) {
        Vec3 entityPos = new Vec3(entity.posX, entity.posY + entity.getEyeHeight(), entity.posZ);
        return getRotationsToPosition(entityPos);
    }
    
    /**
     * Smoothly rotate to target rotations
     */
    public static void smoothRotateTo(float yaw, float pitch) {
        targetYaw = yaw;
        targetPitch = pitch;
        isRotating = true;
    }
    
    /**
     * Update smooth rotation (call this every tick)
     */
    public static void updateSmoothRotation() {
        EntityPlayer player = mc.thePlayer;
        if (player == null || !isRotating) return;
        
        float currentYaw = player.rotationYaw;
        float currentPitch = player.rotationPitch;
        
        // Calculate the difference
        float yawDiff = MathHelper.wrapAngleTo180_float(targetYaw - currentYaw);
        float pitchDiff = MathHelper.wrapAngleTo180_float(targetPitch - currentPitch);
        
        // Apply rotation speed limit
        float maxRotation = ROTATION_SPEED;
        
        if (Math.abs(yawDiff) > maxRotation) {
            yawDiff = Math.signum(yawDiff) * maxRotation;
        }
        
        if (Math.abs(pitchDiff) > maxRotation) {
            pitchDiff = Math.signum(pitchDiff) * maxRotation;
        }
        
        // Apply the rotation
        player.rotationYaw += yawDiff;
        player.rotationPitch += pitchDiff;
        
        // Clamp pitch
        player.rotationPitch = MathHelper.clamp_float(player.rotationPitch, -90.0f, 90.0f);
        
        // Check if we've reached the target
        if (Math.abs(yawDiff) < 1.0f && Math.abs(pitchDiff) < 1.0f) {
            isRotating = false;
        }
    }
    
    /**
     * Instantly look at a position
     */
    public static void lookAtPosition(Vec3 position) {
        float[] rotations = getRotationsToPosition(position);
        setPlayerRotation(rotations[0], rotations[1]);
    }
    
    /**
     * Instantly look at a block
     */
    public static void lookAtBlock(BlockPos pos) {
        float[] rotations = getRotationsToBlock(pos);
        setPlayerRotation(rotations[0], rotations[1]);
    }
    
    /**
     * Instantly look at an entity
     */
    public static void lookAtEntity(Entity entity) {
        float[] rotations = getRotationsToEntity(entity);
        setPlayerRotation(rotations[0], rotations[1]);
    }
    
    /**
     * Set player rotation directly
     */
    public static void setPlayerRotation(float yaw, float pitch) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return;
        
        player.rotationYaw = yaw;
        player.rotationPitch = MathHelper.clamp_float(pitch, -90.0f, 90.0f);
    }
    
    /**
     * Get the distance between two angles
     */
    public static float getAngleDifference(float angle1, float angle2) {
        return Math.abs(MathHelper.wrapAngleTo180_float(angle1 - angle2));
    }
    
    /**
     * Check if the player is looking at a position within a tolerance
     */
    public static boolean isLookingAt(Vec3 position, float tolerance) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        float[] targetRotations = getRotationsToPosition(position);
        float yawDiff = getAngleDifference(player.rotationYaw, targetRotations[0]);
        float pitchDiff = getAngleDifference(player.rotationPitch, targetRotations[1]);
        
        return yawDiff <= tolerance && pitchDiff <= tolerance;
    }
    
    /**
     * Check if the player is looking at a block within a tolerance
     */
    public static boolean isLookingAtBlock(BlockPos pos, float tolerance) {
        Vec3 blockCenter = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
        return isLookingAt(blockCenter, tolerance);
    }
    
    /**
     * Check if the player is looking at an entity within a tolerance
     */
    public static boolean isLookingAtEntity(Entity entity, float tolerance) {
        Vec3 entityPos = new Vec3(entity.posX, entity.posY + entity.getEyeHeight(), entity.posZ);
        return isLookingAt(entityPos, tolerance);
    }
    
    /**
     * Add random jitter to rotations for more human-like movement
     */
    public static float[] addRotationJitter(float[] rotations, float maxJitter) {
        float jitterYaw = (float) (Math.random() - 0.5) * 2 * maxJitter;
        float jitterPitch = (float) (Math.random() - 0.5) * 2 * maxJitter;
        
        return new float[]{
            rotations[0] + jitterYaw,
            rotations[1] + jitterPitch
        };
    }
    
    /**
     * Get the current player rotation
     */
    public static float[] getCurrentRotation() {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return new float[]{0, 0};
        
        return new float[]{player.rotationYaw, player.rotationPitch};
    }
    
    /**
     * Check if smooth rotation is in progress
     */
    public static boolean isRotating() {
        return isRotating;
    }
    
    /**
     * Stop smooth rotation
     */
    public static void stopRotation() {
        isRotating = false;
    }
    
    /**
     * Calculate the angle between player's look direction and a position
     */
    public static float getAngleToPosition(Vec3 position) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return 0;
        
        float[] targetRotations = getRotationsToPosition(position);
        float yawDiff = getAngleDifference(player.rotationYaw, targetRotations[0]);
        float pitchDiff = getAngleDifference(player.rotationPitch, targetRotations[1]);
        
        return (float) Math.sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff);
    }
    
    /**
     * Get a random rotation within a cone around the current look direction
     */
    public static float[] getRandomRotationInCone(float coneAngle) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return new float[]{0, 0};
        
        float currentYaw = player.rotationYaw;
        float currentPitch = player.rotationPitch;
        
        float randomYaw = currentYaw + (float) (Math.random() - 0.5) * 2 * coneAngle;
        float randomPitch = currentPitch + (float) (Math.random() - 0.5) * 2 * coneAngle;
        
        randomPitch = MathHelper.clamp_float(randomPitch, -90.0f, 90.0f);
        
        return new float[]{randomYaw, randomPitch};
    }
}
