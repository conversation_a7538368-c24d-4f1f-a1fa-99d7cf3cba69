package com.newera.modules;

import com.newera.NewEraMod;
import com.newera.core.ConfigManager;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.world.World;
import net.minecraftforge.common.MinecraftForge;

/**
 * Base class for all NewEra modules
 * Provides common functionality and structure for automation modules
 */
public abstract class BaseModule {
    
    protected final ConfigManager configManager;
    protected final Minecraft mc;
    protected boolean enabled;
    protected long lastTickTime;
    protected int tickCounter;
    
    // Common timing variables
    protected long lastActionTime;
    protected long actionDelay;
    
    public BaseModule(ConfigManager configManager) {
        this.configManager = configManager;
        this.mc = Minecraft.getMinecraft();
        this.enabled = false;
        this.lastTickTime = System.currentTimeMillis();
        this.tickCounter = 0;
        this.lastActionTime = 0;
        this.actionDelay = 1000; // Default 1 second delay
    }
    
    /**
     * Called when the module is enabled
     * Override to implement module-specific initialization
     */
    public void onEnable() {
        enabled = true;
        lastActionTime = System.currentTimeMillis();
        tickCounter = 0;
        
        // Register event handlers if needed
        registerEventHandlers();
        
        NewEraMod.debugLog(getClass().getSimpleName() + " enabled");
    }
    
    /**
     * Called when the module is disabled
     * Override to implement module-specific cleanup
     */
    public void onDisable() {
        enabled = false;
        
        // Unregister event handlers if needed
        unregisterEventHandlers();
        
        NewEraMod.debugLog(getClass().getSimpleName() + " disabled");
    }
    
    /**
     * Called every client tick when the module is enabled
     * Override to implement module-specific logic
     */
    public abstract void onTick();
    
    /**
     * Register event handlers for this module
     * Override if the module needs to listen to Minecraft events
     */
    protected void registerEventHandlers() {
        // Default implementation does nothing
        // Override in subclasses that need event handling
    }
    
    /**
     * Unregister event handlers for this module
     * Override if the module registered event handlers
     */
    protected void unregisterEventHandlers() {
        // Default implementation does nothing
        // Override in subclasses that registered event handlers
    }
    
    /**
     * Check if the module is enabled
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Get the current player
     */
    protected EntityPlayer getPlayer() {
        return mc.thePlayer;
    }
    
    /**
     * Get the current world
     */
    protected World getWorld() {
        return mc.theWorld;
    }
    
    /**
     * Check if the player and world are available
     */
    protected boolean isInGame() {
        return mc.thePlayer != null && mc.theWorld != null && !mc.isGamePaused();
    }
    
    /**
     * Check if enough time has passed since the last action
     */
    protected boolean canPerformAction() {
        return System.currentTimeMillis() - lastActionTime >= actionDelay;
    }
    
    /**
     * Update the last action time
     */
    protected void updateActionTime() {
        lastActionTime = System.currentTimeMillis();
    }
    
    /**
     * Set the action delay for this module
     */
    protected void setActionDelay(long delay) {
        this.actionDelay = delay;
    }
    
    /**
     * Get a random delay within a range (for more human-like behavior)
     */
    protected long getRandomDelay(long min, long max) {
        if (min >= max) return min;
        return min + (long) (Math.random() * (max - min));
    }
    
    /**
     * Check if the module should perform a random event
     * Used to make automation less predictable
     */
    protected boolean shouldPerformRandomEvent(double chance) {
        return Math.random() < chance;
    }
    
    /**
     * Update tick counter and timing
     */
    protected void updateTiming() {
        tickCounter++;
        lastTickTime = System.currentTimeMillis();
    }
    
    /**
     * Get the number of ticks since module was enabled
     */
    protected int getTickCounter() {
        return tickCounter;
    }
    
    /**
     * Check if a certain number of ticks have passed
     */
    protected boolean hasTicksPassed(int ticks) {
        return tickCounter % ticks == 0;
    }
    
    /**
     * Get module name for logging and identification
     */
    public String getModuleName() {
        return getClass().getSimpleName().replace("Module", "").toLowerCase();
    }
    
    /**
     * Log debug message for this module
     */
    protected void debugLog(String message) {
        NewEraMod.debugLog("[" + getModuleName() + "] " + message);
    }
    
    /**
     * Log info message for this module
     */
    protected void info(String message) {
        NewEraMod.info("[" + getModuleName() + "] " + message);
    }
    
    /**
     * Log warning message for this module
     */
    protected void warn(String message) {
        NewEraMod.warn("[" + getModuleName() + "] " + message);
    }
    
    /**
     * Log error message for this module
     */
    protected void error(String message) {
        NewEraMod.error("[" + getModuleName() + "] " + message);
    }
    
    /**
     * Log error message with exception for this module
     */
    protected void error(String message, Throwable throwable) {
        NewEraMod.error("[" + getModuleName() + "] " + message, throwable);
    }
}
