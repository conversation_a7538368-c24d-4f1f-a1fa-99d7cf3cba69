package com.newera.gui;

import com.newera.NewEraMod;
import com.newera.core.ConfigManager;
import com.newera.core.ModuleManager;
import com.newera.gui.widgets.GuiSlider;
import com.newera.gui.widgets.GuiToggleButton;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.client.renderer.GlStateManager;
import org.lwjgl.input.Mouse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Main GUI for NewEra mod configuration
 */
public class GuiNewEra extends GuiScreen {
    
    private static final int GUI_WIDTH = 400;
    private static final int GUI_HEIGHT = 300;
    
    private int guiLeft;
    private int guiTop;
    
    // Button IDs
    private static final int BUTTON_FISHING_TOGGLE = 1;
    private static final int BUTTON_COMBAT_TOGGLE = 2;
    private static final int BUTTON_MINING_TOGGLE = 3;
    private static final int BUTTON_FARMING_TOGGLE = 4;
    private static final int BUTTON_LEARNING_TOGGLE = 5;
    private static final int BUTTON_DEBUG_TOGGLE = 6;
    private static final int BUTTON_CLOSE = 7;
    
    private static final int SLIDER_FISHING_DELAY = 10;
    private static final int SLIDER_COMBAT_DELAY = 11;
    private static final int SLIDER_MINING_RANGE = 12;
    private static final int SLIDER_FARMING_RANGE = 13;
    
    // GUI components
    private final List<GuiSlider> sliders = new ArrayList<>();
    private ModuleManager moduleManager;
    private ConfigManager configManager;
    
    @Override
    public void initGui() {
        super.initGui();
        
        this.guiLeft = (this.width - GUI_WIDTH) / 2;
        this.guiTop = (this.height - GUI_HEIGHT) / 2;
        
        this.moduleManager = NewEraMod.getModuleManager();
        this.configManager = NewEraMod.getConfigManager();
        
        if (moduleManager == null || configManager == null) {
            this.mc.displayGuiScreen(null);
            return;
        }
        
        initializeButtons();
        initializeSliders();
    }
    
    /**
     * Initialize toggle buttons for modules
     */
    private void initializeButtons() {
        int buttonY = guiTop + 30;
        int buttonSpacing = 25;
        
        // Module toggle buttons
        this.buttonList.add(new GuiToggleButton(BUTTON_FISHING_TOGGLE, guiLeft + 10, buttonY, 
            "Fishing: ON", "Fishing: OFF", moduleManager.isModuleEnabled("fishing")));
        
        buttonY += buttonSpacing;
        this.buttonList.add(new GuiToggleButton(BUTTON_COMBAT_TOGGLE, guiLeft + 10, buttonY,
            "Combat: ON", "Combat: OFF", moduleManager.isModuleEnabled("combat")));
        
        buttonY += buttonSpacing;
        this.buttonList.add(new GuiToggleButton(BUTTON_MINING_TOGGLE, guiLeft + 10, buttonY,
            "Mining: ON", "Mining: OFF", moduleManager.isModuleEnabled("mining")));
        
        buttonY += buttonSpacing;
        this.buttonList.add(new GuiToggleButton(BUTTON_FARMING_TOGGLE, guiLeft + 10, buttonY,
            "Farming: ON", "Farming: OFF", moduleManager.isModuleEnabled("farming")));
        
        buttonY += buttonSpacing;
        this.buttonList.add(new GuiToggleButton(BUTTON_LEARNING_TOGGLE, guiLeft + 10, buttonY,
            "Learning: ON", "Learning: OFF", moduleManager.isModuleEnabled("learning")));
        
        buttonY += buttonSpacing;
        this.buttonList.add(new GuiToggleButton(BUTTON_DEBUG_TOGGLE, guiLeft + 10, buttonY,
            "Debug: ON", "Debug: OFF", configManager.getGeneralConfig().debug));
        
        // Close button
        this.buttonList.add(new GuiButton(BUTTON_CLOSE, guiLeft + GUI_WIDTH - 60, guiTop + GUI_HEIGHT - 30, 
            50, 20, "Close"));
    }
    
    /**
     * Initialize sliders for configuration
     */
    private void initializeSliders() {
        int sliderX = guiLeft + 220;
        int sliderY = guiTop + 30;
        int sliderSpacing = 25;
        
        // Fishing delay slider
        GuiSlider fishingDelaySlider = new GuiSlider(SLIDER_FISHING_DELAY, sliderX, sliderY, 150, 20,
            "Delay: ", "ms", 500, 5000, configManager.getFishingConfig().minDelay);
        sliders.add(fishingDelaySlider);
        this.buttonList.add(fishingDelaySlider);
        
        sliderY += sliderSpacing;
        // Combat delay slider
        GuiSlider combatDelaySlider = new GuiSlider(SLIDER_COMBAT_DELAY, sliderX, sliderY, 150, 20,
            "Attack: ", "ms", 200, 2000, configManager.getCombatConfig().attackDelay);
        sliders.add(combatDelaySlider);
        this.buttonList.add(combatDelaySlider);
        
        sliderY += sliderSpacing;
        // Mining range slider
        GuiSlider miningRangeSlider = new GuiSlider(SLIDER_MINING_RANGE, sliderX, sliderY, 150, 20,
            "Range: ", " blocks", 1, 20, configManager.getMiningConfig().range);
        sliders.add(miningRangeSlider);
        this.buttonList.add(miningRangeSlider);
        
        sliderY += sliderSpacing;
        // Farming range slider
        GuiSlider farmingRangeSlider = new GuiSlider(SLIDER_FARMING_RANGE, sliderX, sliderY, 150, 20,
            "Range: ", " blocks", 1, 20, configManager.getFarmingConfig().range);
        sliders.add(farmingRangeSlider);
        this.buttonList.add(farmingRangeSlider);
    }
    
    @Override
    protected void actionPerformed(GuiButton button) throws IOException {
        if (button instanceof GuiToggleButton) {
            GuiToggleButton toggleButton = (GuiToggleButton) button;
            toggleButton.toggle();
            
            switch (button.id) {
                case BUTTON_FISHING_TOGGLE:
                    moduleManager.toggleModule("fishing");
                    break;
                case BUTTON_COMBAT_TOGGLE:
                    moduleManager.toggleModule("combat");
                    break;
                case BUTTON_MINING_TOGGLE:
                    moduleManager.toggleModule("mining");
                    break;
                case BUTTON_FARMING_TOGGLE:
                    moduleManager.toggleModule("farming");
                    break;
                case BUTTON_LEARNING_TOGGLE:
                    moduleManager.toggleModule("learning");
                    break;
                case BUTTON_DEBUG_TOGGLE:
                    configManager.getGeneralConfig().debug = toggleButton.isToggled();
                    configManager.saveConfig();
                    break;
            }
        } else if (button.id == BUTTON_CLOSE) {
            this.mc.displayGuiScreen(null);
        }
    }
    
    @Override
    protected void mouseClicked(int mouseX, int mouseY, int mouseButton) throws IOException {
        super.mouseClicked(mouseX, mouseY, mouseButton);
        
        // Handle slider clicks
        for (GuiSlider slider : sliders) {
            if (slider.mousePressed(this.mc, mouseX, mouseY)) {
                updateConfigFromSlider(slider);
            }
        }
    }
    
    @Override
    protected void mouseClickMove(int mouseX, int mouseY, int clickedMouseButton, long timeSinceLastClick) {
        super.mouseClickMove(mouseX, mouseY, clickedMouseButton, timeSinceLastClick);
        
        // Handle slider dragging
        for (GuiSlider slider : sliders) {
            if (slider.isDragging()) {
                slider.mouseDragged(this.mc, mouseX, mouseY);
                updateConfigFromSlider(slider);
            }
        }
    }
    
    @Override
    protected void mouseReleased(int mouseX, int mouseY, int state) {
        super.mouseReleased(mouseX, mouseY, state);
        
        // Handle slider release
        for (GuiSlider slider : sliders) {
            slider.mouseReleased(mouseX, mouseY);
        }
    }
    
    /**
     * Update configuration based on slider values
     */
    private void updateConfigFromSlider(GuiSlider slider) {
        switch (slider.id) {
            case SLIDER_FISHING_DELAY:
                configManager.getFishingConfig().minDelay = (int) slider.getValue();
                configManager.getFishingConfig().maxDelay = (int) (slider.getValue() * 1.5);
                break;
            case SLIDER_COMBAT_DELAY:
                configManager.getCombatConfig().attackDelay = (int) slider.getValue();
                break;
            case SLIDER_MINING_RANGE:
                configManager.getMiningConfig().range = (int) slider.getValue();
                break;
            case SLIDER_FARMING_RANGE:
                configManager.getFarmingConfig().range = (int) slider.getValue();
                break;
        }
        configManager.saveConfig();
    }
    
    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        // Draw background
        this.drawDefaultBackground();
        
        // Draw GUI background
        GlStateManager.color(1.0f, 1.0f, 1.0f, 1.0f);
        drawRect(guiLeft, guiTop, guiLeft + GUI_WIDTH, guiTop + GUI_HEIGHT, 0xC0101010);
        drawRect(guiLeft + 1, guiTop + 1, guiLeft + GUI_WIDTH - 1, guiTop + GUI_HEIGHT - 1, 0xFF383838);
        
        // Draw title
        String title = "NewEra Control Panel";
        int titleWidth = this.fontRendererObj.getStringWidth(title);
        this.fontRendererObj.drawString(title, guiLeft + (GUI_WIDTH - titleWidth) / 2, guiTop + 10, 0xFFFFFF);
        
        // Draw section headers
        this.fontRendererObj.drawString("Modules", guiLeft + 10, guiTop + 20, 0xCCCCCC);
        this.fontRendererObj.drawString("Configuration", guiLeft + 220, guiTop + 20, 0xCCCCCC);
        
        // Draw module status information
        drawModuleStatus();
        
        // Draw buttons and sliders
        super.drawScreen(mouseX, mouseY, partialTicks);
        
        // Draw tooltips
        drawTooltips(mouseX, mouseY);
    }
    
    /**
     * Draw module status information
     */
    private void drawModuleStatus() {
        int statusX = guiLeft + 10;
        int statusY = guiTop + 180;
        
        this.fontRendererObj.drawString("Status:", statusX, statusY, 0xCCCCCC);
        statusY += 12;
        
        if (moduleManager.isModuleEnabled("fishing")) {
            this.fontRendererObj.drawString("§aFishing active", statusX, statusY, 0xFFFFFF);
            statusY += 10;
        }
        
        if (moduleManager.isModuleEnabled("combat")) {
            this.fontRendererObj.drawString("§cCombat active", statusX, statusY, 0xFFFFFF);
            statusY += 10;
        }
        
        if (moduleManager.isModuleEnabled("mining")) {
            this.fontRendererObj.drawString("§6Mining active", statusX, statusY, 0xFFFFFF);
            statusY += 10;
        }
        
        if (moduleManager.isModuleEnabled("farming")) {
            this.fontRendererObj.drawString("§2Farming active", statusX, statusY, 0xFFFFFF);
            statusY += 10;
        }
        
        if (moduleManager.isModuleEnabled("learning")) {
            this.fontRendererObj.drawString("§bLearning active", statusX, statusY, 0xFFFFFF);
            statusY += 10;
        }
    }
    
    /**
     * Draw tooltips for GUI elements
     */
    private void drawTooltips(int mouseX, int mouseY) {
        for (GuiButton button : this.buttonList) {
            if (button.isMouseOver() && button instanceof GuiToggleButton) {
                String tooltip = getTooltipForButton(button.id);
                if (tooltip != null) {
                    this.drawHoveringText(tooltip, mouseX, mouseY);
                }
            }
        }
    }
    
    /**
     * Get tooltip text for a button
     */
    private String getTooltipForButton(int buttonId) {
        switch (buttonId) {
            case BUTTON_FISHING_TOGGLE:
                return "Automatically fish and fight spawned mobs";
            case BUTTON_COMBAT_TOGGLE:
                return "Automatically attack nearby hostile mobs";
            case BUTTON_MINING_TOGGLE:
                return "Automatically mine target ores";
            case BUTTON_FARMING_TOGGLE:
                return "Automatically harvest and replant crops";
            case BUTTON_LEARNING_TOGGLE:
                return "Learn patterns and optimize parameters";
            case BUTTON_DEBUG_TOGGLE:
                return "Enable debug logging and chat messages";
            default:
                return null;
        }
    }
    
    @Override
    public boolean doesGuiPauseGame() {
        return false; // Don't pause the game when GUI is open
    }
    
    @Override
    public void onGuiClosed() {
        super.onGuiClosed();
        // Save configuration when GUI is closed
        configManager.saveConfig();
    }
}
