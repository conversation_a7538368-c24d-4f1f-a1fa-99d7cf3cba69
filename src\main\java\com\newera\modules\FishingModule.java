package com.newera.modules;

import com.newera.core.ConfigManager;
import com.newera.utils.BlockUtils;
import com.newera.utils.InventoryUtils;
import com.newera.utils.RotationUtils;
import net.minecraft.entity.item.EntityFishHook;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemFishingRod;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MovingObjectPosition;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

import java.util.List;
import java.util.Random;

/**
 * Fishing automation module
 * Handles automatic fishing with mob detection and random events
 */
public class FishingModule extends BaseModule {
    
    private final ConfigManager.FishingConfig config;
    private final Random random = new Random();
    
    // Fishing state
    private FishingState state = FishingState.IDLE;
    private EntityFishHook currentHook = null;
    private long lastCastTime = 0;
    private long nextCastDelay = 1000;
    private BlockPos currentFishingSpot = null;
    private int randomEventCounter = 0;
    
    // Mob combat
    private EntityMob targetMob = null;
    private long lastAttackTime = 0;
    
    private enum FishingState {
        IDLE,
        CASTING,
        WAITING_FOR_BITE,
        REELING_IN,
        FIGHTING_MOB,
        MOVING_TO_SPOT
    }
    
    public FishingModule(ConfigManager configManager) {
        super(configManager);
        this.config = configManager.getFishingConfig();
    }
    
    @Override
    protected void registerEventHandlers() {
        MinecraftForge.EVENT_BUS.register(this);
    }
    
    @Override
    protected void unregisterEventHandlers() {
        MinecraftForge.EVENT_BUS.unregister(this);
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        state = FishingState.IDLE;
        currentHook = null;
        targetMob = null;
        findFishingSpot();
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        if (currentHook != null) {
            reelIn();
        }
        state = FishingState.IDLE;
    }
    
    @Override
    public void onTick() {
        if (!isInGame()) return;
        
        updateTiming();
        
        // Handle random events
        handleRandomEvents();
        
        // Main fishing logic
        switch (state) {
            case IDLE:
                handleIdleState();
                break;
            case CASTING:
                handleCastingState();
                break;
            case WAITING_FOR_BITE:
                handleWaitingState();
                break;
            case REELING_IN:
                handleReelingState();
                break;
            case FIGHTING_MOB:
                handleCombatState();
                break;
            case MOVING_TO_SPOT:
                handleMovingState();
                break;
        }
        
        // Update smooth rotation
        RotationUtils.updateSmoothRotation();
    }
    
    /**
     * Handle idle state - prepare for fishing
     */
    private void handleIdleState() {
        if (!canPerformAction()) return;
        
        // Check if we have a fishing rod
        int rodSlot = config.rodSlot >= 0 ? config.rodSlot : InventoryUtils.findFishingRod();
        if (rodSlot < 0) {
            debugLog("No fishing rod found");
            return;
        }
        
        // Switch to fishing rod
        InventoryUtils.switchToSlot(rodSlot);
        
        // Find fishing spot if needed
        if (currentFishingSpot == null) {
            findFishingSpot();
            if (currentFishingSpot == null) {
                debugLog("No fishing spot found");
                return;
            }
        }
        
        // Check if we're close enough to fishing spot
        if (!BlockUtils.canReachBlock(currentFishingSpot)) {
            state = FishingState.MOVING_TO_SPOT;
            return;
        }
        
        // Start casting
        state = FishingState.CASTING;
        updateActionTime();
    }
    
    /**
     * Handle casting state
     */
    private void handleCastingState() {
        if (currentFishingSpot == null) {
            state = FishingState.IDLE;
            return;
        }
        
        // Look at fishing spot
        RotationUtils.smoothRotateTo(
            RotationUtils.getRotationsToBlock(currentFishingSpot)[0],
            RotationUtils.getRotationsToBlock(currentFishingSpot)[1]
        );
        
        // Wait for rotation to complete
        if (RotationUtils.isRotating()) return;
        
        // Cast the line
        if (castLine()) {
            state = FishingState.WAITING_FOR_BITE;
            lastCastTime = System.currentTimeMillis();
            nextCastDelay = getRandomDelay(config.minDelay, config.maxDelay);
            debugLog("Cast fishing line");
        } else {
            state = FishingState.IDLE;
        }
    }
    
    /**
     * Handle waiting for bite state
     */
    private void handleWaitingState() {
        // Check for mobs nearby
        EntityMob nearbyMob = findNearbyMob();
        if (nearbyMob != null) {
            targetMob = nearbyMob;
            state = FishingState.FIGHTING_MOB;
            debugLog("Mob detected, switching to combat");
            return;
        }
        
        // Check if hook exists and if fish is biting
        if (currentHook == null) {
            state = FishingState.IDLE;
            return;
        }
        
        // Check for bite (hook motion indicates fish)
        if (isFishBiting()) {
            state = FishingState.REELING_IN;
            debugLog("Fish is biting, reeling in");
        }
        
        // Timeout check
        if (System.currentTimeMillis() - lastCastTime > 30000) { // 30 second timeout
            reelIn();
            state = FishingState.IDLE;
            debugLog("Fishing timeout, recasting");
        }
    }
    
    /**
     * Handle reeling in state
     */
    private void handleReelingState() {
        if (reelIn()) {
            state = FishingState.IDLE;
            updateActionTime();
            debugLog("Reeled in catch");
        }
    }
    
    /**
     * Handle combat state
     */
    private void handleCombatState() {
        if (targetMob == null || targetMob.isDead) {
            targetMob = null;
            state = FishingState.IDLE;
            return;
        }
        
        // Switch to weapon
        int weaponSlot = config.weaponSlot >= 0 ? config.weaponSlot : InventoryUtils.findBestWeapon();
        if (weaponSlot >= 0) {
            InventoryUtils.switchToSlot(weaponSlot);
        }
        
        // Look at mob
        RotationUtils.smoothRotateTo(
            RotationUtils.getRotationsToEntity(targetMob)[0],
            RotationUtils.getRotationsToEntity(targetMob)[1]
        );
        
        // Attack if ready
        if (canAttack() && !RotationUtils.isRotating()) {
            attackMob(targetMob);
        }
        
        // Check if mob is too far
        if (getPlayer().getDistanceToEntity(targetMob) > 10) {
            targetMob = null;
            state = FishingState.IDLE;
        }
    }
    
    /**
     * Handle moving to fishing spot state
     */
    private void handleMovingState() {
        if (currentFishingSpot == null) {
            state = FishingState.IDLE;
            return;
        }
        
        // Simple movement towards fishing spot
        if (BlockUtils.canReachBlock(currentFishingSpot)) {
            state = FishingState.IDLE;
        } else {
            // Move towards spot (simplified)
            RotationUtils.smoothRotateTo(
                RotationUtils.getRotationsToBlock(currentFishingSpot)[0],
                0 // Keep pitch level for walking
            );
        }
    }
    
    /**
     * Cast fishing line
     */
    private boolean castLine() {
        if (!InventoryUtils.isHoldingFishingRod()) return false;
        
        // Right click to cast
        mc.rightClickMouse();
        
        // Find the hook entity
        currentHook = findFishHook();
        
        return currentHook != null;
    }
    
    /**
     * Reel in fishing line
     */
    private boolean reelIn() {
        if (currentHook == null) return false;
        
        // Right click to reel in
        mc.rightClickMouse();
        currentHook = null;
        
        return true;
    }
    
    /**
     * Find fishing hook entity
     */
    private EntityFishHook findFishHook() {
        for (Object entity : getWorld().loadedEntityList) {
            if (entity instanceof EntityFishHook) {
                EntityFishHook hook = (EntityFishHook) entity;
                if (hook.angler == getPlayer()) {
                    return hook;
                }
            }
        }
        return null;
    }
    
    /**
     * Check if fish is biting
     */
    private boolean isFishBiting() {
        if (currentHook == null) return false;
        
        // Check hook motion - if it's moving significantly, fish might be biting
        double motionThreshold = 0.1;
        return Math.abs(currentHook.motionX) > motionThreshold ||
               Math.abs(currentHook.motionY) > motionThreshold ||
               Math.abs(currentHook.motionZ) > motionThreshold;
    }
    
    /**
     * Find nearby hostile mobs
     */
    private EntityMob findNearbyMob() {
        EntityPlayer player = getPlayer();
        if (player == null) return null;
        
        for (Object entity : getWorld().loadedEntityList) {
            if (entity instanceof EntityMob) {
                EntityMob mob = (EntityMob) entity;
                if (player.getDistanceToEntity(mob) <= 8 && !mob.isDead) {
                    return mob;
                }
            }
        }
        return null;
    }
    
    /**
     * Attack a mob
     */
    private void attackMob(EntityMob mob) {
        mc.playerController.attackEntity(getPlayer(), mob);
        getPlayer().swingItem();
        lastAttackTime = System.currentTimeMillis();
        debugLog("Attacked mob: " + mob.getName());
    }
    
    /**
     * Check if we can attack
     */
    private boolean canAttack() {
        return System.currentTimeMillis() - lastAttackTime > 500; // 500ms cooldown
    }
    
    /**
     * Find a suitable fishing spot
     */
    private void findFishingSpot() {
        EntityPlayer player = getPlayer();
        if (player == null) return;
        
        BlockPos playerPos = new BlockPos(player.posX, player.posY, player.posZ);
        List<BlockPos> waterBlocks = BlockUtils.findWaterBlocks(playerPos, config.range);
        
        if (!waterBlocks.isEmpty()) {
            // Choose a random water block
            currentFishingSpot = waterBlocks.get(random.nextInt(waterBlocks.size()));
            debugLog("Found fishing spot at " + currentFishingSpot);
        }
    }
    
    /**
     * Handle random events for more human-like behavior
     */
    private void handleRandomEvents() {
        if (!config.randomEvents) return;
        
        randomEventCounter++;
        
        // Random area switching (every 200-400 ticks)
        if (randomEventCounter > 200 + random.nextInt(200)) {
            if (shouldPerformRandomEvent(0.3)) { // 30% chance
                findFishingSpot();
                debugLog("Random event: Changed fishing spot");
            }
            randomEventCounter = 0;
        }
        
        // Random crouching
        if (shouldPerformRandomEvent(0.001)) { // 0.1% chance per tick
            mc.gameSettings.keyBindSneak.pressed = !mc.gameSettings.keyBindSneak.pressed;
            debugLog("Random event: Toggled crouching");
        }
    }
}
