package com.newera.utils;

import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.*;
import net.minecraft.util.BlockPos;

/**
 * Utility class for inventory-related operations
 */
public class InventoryUtils {
    
    private static final Minecraft mc = Minecraft.getMinecraft();
    
    /**
     * Find the slot of a specific item type in the hotbar
     */
    public static int findItemInHotbar(Class<? extends Item> itemClass) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return -1;
        
        for (int i = 0; i < 9; i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && itemClass.isInstance(stack.getItem())) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * Find the slot of a specific item in the hotbar
     */
    public static int findItemInHotbar(Item item) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return -1;
        
        for (int i = 0; i < 9; i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() == item) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * Find fishing rod in hotbar
     */
    public static int findFishingRod() {
        return findItemInHotbar(ItemFishingRod.class);
    }
    
    /**
     * Find sword in hotbar
     */
    public static int findSword() {
        return findItemInHotbar(ItemSword.class);
    }
    
    /**
     * Find pickaxe in hotbar
     */
    public static int findPickaxe() {
        return findItemInHotbar(ItemPickaxe.class);
    }
    
    /**
     * Find axe in hotbar
     */
    public static int findAxe() {
        return findItemInHotbar(ItemAxe.class);
    }
    
    /**
     * Find hoe in hotbar
     */
    public static int findHoe() {
        return findItemInHotbar(ItemHoe.class);
    }
    
    /**
     * Find shovel in hotbar
     */
    public static int findShovel() {
        return findItemInHotbar(ItemSpade.class);
    }
    
    /**
     * Find the best tool for a specific block
     */
    public static int findBestTool(BlockPos pos) {
        EntityPlayer player = mc.thePlayer;
        if (player == null || mc.theWorld == null) return -1;
        
        // Get the block at the position
        net.minecraft.block.Block block = mc.theWorld.getBlockState(pos).getBlock();
        
        int bestSlot = -1;
        float bestSpeed = 1.0f;
        
        for (int i = 0; i < 9; i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null) {
                float speed = stack.getStrVsBlock(block);
                if (speed > bestSpeed) {
                    bestSpeed = speed;
                    bestSlot = i;
                }
            }
        }
        
        return bestSlot;
    }
    
    /**
     * Find weapon with highest damage
     */
    public static int findBestWeapon() {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return -1;
        
        int bestSlot = -1;
        float bestDamage = 0.0f;
        
        for (int i = 0; i < 9; i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() instanceof ItemTool) {
                ItemTool tool = (ItemTool) stack.getItem();
                float damage = tool.getDamageVsEntity();
                if (damage > bestDamage) {
                    bestDamage = damage;
                    bestSlot = i;
                }
            } else if (stack != null && stack.getItem() instanceof ItemSword) {
                ItemSword sword = (ItemSword) stack.getItem();
                float damage = sword.getDamageVsEntity();
                if (damage > bestDamage) {
                    bestDamage = damage;
                    bestSlot = i;
                }
            }
        }
        
        return bestSlot;
    }
    
    /**
     * Switch to a specific hotbar slot
     */
    public static void switchToSlot(int slot) {
        if (slot >= 0 && slot < 9 && mc.thePlayer != null) {
            mc.thePlayer.inventory.currentItem = slot;
        }
    }
    
    /**
     * Get the currently held item
     */
    public static ItemStack getCurrentItem() {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return null;
        return player.inventory.getCurrentItem();
    }
    
    /**
     * Check if the player is holding a specific item type
     */
    public static boolean isHolding(Class<? extends Item> itemClass) {
        ItemStack current = getCurrentItem();
        return current != null && itemClass.isInstance(current.getItem());
    }
    
    /**
     * Check if the player is holding a fishing rod
     */
    public static boolean isHoldingFishingRod() {
        return isHolding(ItemFishingRod.class);
    }
    
    /**
     * Check if the player is holding a weapon
     */
    public static boolean isHoldingWeapon() {
        return isHolding(ItemSword.class) || isHolding(ItemTool.class);
    }
    
    /**
     * Check if the player is holding a tool
     */
    public static boolean isHoldingTool() {
        return isHolding(ItemTool.class);
    }
    
    /**
     * Get the durability of the currently held item
     */
    public static int getCurrentItemDurability() {
        ItemStack current = getCurrentItem();
        if (current == null) return 0;
        return current.getMaxDamage() - current.getItemDamage();
    }
    
    /**
     * Check if the currently held item is about to break
     */
    public static boolean isCurrentItemAboutToBreak() {
        return getCurrentItemDurability() <= 5;
    }
    
    /**
     * Count items of a specific type in inventory
     */
    public static int countItems(Item item) {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return 0;
        
        int count = 0;
        for (int i = 0; i < player.inventory.getSizeInventory(); i++) {
            ItemStack stack = player.inventory.getStackInSlot(i);
            if (stack != null && stack.getItem() == item) {
                count += stack.stackSize;
            }
        }
        return count;
    }
    
    /**
     * Check if inventory has space
     */
    public static boolean hasInventorySpace() {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return false;
        
        for (int i = 0; i < player.inventory.getSizeInventory(); i++) {
            if (player.inventory.getStackInSlot(i) == null) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get the number of empty slots in inventory
     */
    public static int getEmptySlots() {
        EntityPlayer player = mc.thePlayer;
        if (player == null) return 0;
        
        int empty = 0;
        for (int i = 0; i < player.inventory.getSizeInventory(); i++) {
            if (player.inventory.getStackInSlot(i) == null) {
                empty++;
            }
        }
        return empty;
    }
}
