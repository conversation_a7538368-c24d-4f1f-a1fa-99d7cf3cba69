# NewEra Mod Configuration File
# This file contains all configuration options for the NewEra automation mod
# Edit values carefully and restart Minecraft for changes to take effect

##########################################################################################################
# fishing
#--------------------------------------------------------------------------------------------------------#
# Fishing module configuration - Automates fishing with mob detection
##########################################################################################################

fishing {
    # Enable fishing module
    B:enabled=false

    # Fishing range in blocks (how far to look for water)
    I:range=10

    # Minimum delay between casts (ms) - Lower values = faster but more suspicious
    I:minDelay=1000

    # Maximum delay between casts (ms) - Higher values = more human-like
    I:maxDelay=3000

    # Enable random events (area switching, crouching) for anti-detection
    B:randomEvents=true

    # Weapon slot for killing mobs (-1 for auto-detect best weapon)
    I:weaponSlot=-1

    # Rod slot (-1 for auto-detect fishing rod)
    I:rodSlot=-1
}

##########################################################################################################
# combat
#--------------------------------------------------------------------------------------------------------#
# Combat module configuration - Automates combat with hostile mobs
##########################################################################################################

combat {
    # Enable combat module
    B:enabled=false

    # Combat range in blocks (how far to attack mobs)
    D:range=4.0

    # Attack delay in milliseconds (lower = faster attacks, higher = more human-like)
    I:attackDelay=500

    # Enable line of sight check (prevents attacking through walls)
    B:lineOfSight=true

    # Target mobs (comma separated list of mob names to attack)
    S:targetMobs=Zombie,Skeleton,Creeper,Spider,Enderman

    # Weapon slot (-1 for auto-detect best weapon)
    I:weaponSlot=-1
}

##########################################################################################################
# mining
#--------------------------------------------------------------------------------------------------------#
# Mining module configuration
##########################################################################################################

mining {
    # Enable mining module
    B:enabled=false
    
    # Mining range in blocks
    I:range=5
    
    # Target blocks (comma separated block IDs)
    S:targetBlocks=minecraft:diamond_ore,minecraft:iron_ore,minecraft:gold_ore,minecraft:coal_ore
    
    # Pickaxe slot (-1 for auto-detect)
    I:pickaxeSlot=-1
    
    # Weapon slot for mob spawns (-1 for auto-detect)
    I:weaponSlot=-1
    
    # Enable mob spawn detection
    B:mobSpawnDetection=true
}

##########################################################################################################
# farming
#--------------------------------------------------------------------------------------------------------#
# Farming module configuration
##########################################################################################################

farming {
    # Enable farming module
    B:enabled=false
    
    # Farming range in blocks
    I:range=8
    
    # Target crops (comma separated)
    S:targetCrops=minecraft:wheat,minecraft:carrots,minecraft:potatoes,minecraft:beetroots
    
    # Tool slot (-1 for auto-detect)
    I:toolSlot=-1
    
    # Enable replanting
    B:replant=true
}

##########################################################################################################
# learning
#--------------------------------------------------------------------------------------------------------#
# Learning module configuration
##########################################################################################################

learning {
    # Enable learning module
    B:enabled=false
    
    # Learning update interval (ticks)
    I:updateInterval=100
    
    # Enable behavior pattern analysis
    B:behaviorAnalysis=true
    
    # Enable parameter optimization
    B:parameterOptimization=true
    
    # Enable route learning
    B:routeLearning=true
    
    # Enable anti-cheat detection
    B:antiCheatDetection=true
    
    # Learning sensitivity (0.1 - 1.0)
    D:sensitivity=0.5
}

##########################################################################################################
# general
#--------------------------------------------------------------------------------------------------------#
# General configuration
##########################################################################################################

general {
    # Enable debug mode
    B:debug=false
    
    # GUI key code (default: R = 19)
    I:guiKey=19
    
    # Enable chat notifications
    B:chatNotifications=true
}
