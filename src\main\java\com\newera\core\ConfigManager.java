package com.newera.core;

import net.minecraftforge.common.config.Configuration;
import net.minecraftforge.common.config.Property;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * Configuration manager for NewEra mod
 * Handles loading, saving, and accessing all configuration values
 */
public class ConfigManager {
    
    private Configuration config;
    
    // Configuration categories
    private static final String CATEGORY_FISHING = "fishing";
    private static final String CATEGORY_COMBAT = "combat";
    private static final String CATEGORY_MINING = "mining";
    private static final String CATEGORY_FARMING = "farming";
    private static final String CATEGORY_LEARNING = "learning";
    private static final String CATEGORY_GENERAL = "general";
    
    // Configuration holders
    private FishingConfig fishingConfig;
    private CombatConfig combatConfig;
    private MiningConfig miningConfig;
    private FarmingConfig farmingConfig;
    private LearningConfig learningConfig;
    private GeneralConfig generalConfig;
    
    public ConfigManager(File configFile) {
        config = new Configuration(configFile);
        loadConfig();
    }
    
    /**
     * Load all configuration values from file
     */
    public void loadConfig() {
        config.load();
        
        fishingConfig = new FishingConfig();
        combatConfig = new CombatConfig();
        miningConfig = new MiningConfig();
        farmingConfig = new FarmingConfig();
        learningConfig = new LearningConfig();
        generalConfig = new GeneralConfig();
        
        if (config.hasChanged()) {
            config.save();
        }
    }
    
    /**
     * Save configuration to file
     */
    public void saveConfig() {
        if (config.hasChanged()) {
            config.save();
        }
    }
    
    // Getter methods for configuration objects
    public FishingConfig getFishingConfig() { return fishingConfig; }
    public CombatConfig getCombatConfig() { return combatConfig; }
    public MiningConfig getMiningConfig() { return miningConfig; }
    public FarmingConfig getFarmingConfig() { return farmingConfig; }
    public LearningConfig getLearningConfig() { return learningConfig; }
    public GeneralConfig getGeneralConfig() { return generalConfig; }
    
    /**
     * Fishing module configuration
     */
    public class FishingConfig {
        public boolean enabled;
        public int range;
        public int minDelay;
        public int maxDelay;
        public boolean randomEvents;
        public int weaponSlot;
        public int rodSlot;
        
        public FishingConfig() {
            enabled = config.getBoolean("enabled", CATEGORY_FISHING, false, "Enable fishing module");
            range = config.getInt("range", CATEGORY_FISHING, 10, 1, 50, "Fishing range in blocks");
            minDelay = config.getInt("minDelay", CATEGORY_FISHING, 1000, 100, 10000, "Minimum delay between casts (ms)");
            maxDelay = config.getInt("maxDelay", CATEGORY_FISHING, 3000, 1000, 20000, "Maximum delay between casts (ms)");
            randomEvents = config.getBoolean("randomEvents", CATEGORY_FISHING, true, "Enable random events");
            weaponSlot = config.getInt("weaponSlot", CATEGORY_FISHING, -1, -1, 8, "Weapon slot (-1 for auto-detect)");
            rodSlot = config.getInt("rodSlot", CATEGORY_FISHING, -1, -1, 8, "Rod slot (-1 for auto-detect)");
        }
    }
    
    /**
     * Combat module configuration
     */
    public class CombatConfig {
        public boolean enabled;
        public double range;
        public int attackDelay;
        public boolean lineOfSight;
        public List<String> targetMobs;
        public int weaponSlot;
        
        public CombatConfig() {
            enabled = config.getBoolean("enabled", CATEGORY_COMBAT, false, "Enable combat module");
            range = config.getFloat("range", CATEGORY_COMBAT, 4.0f, 1.0f, 10.0f, "Combat range in blocks");
            attackDelay = config.getInt("attackDelay", CATEGORY_COMBAT, 500, 100, 2000, "Attack delay in milliseconds");
            lineOfSight = config.getBoolean("lineOfSight", CATEGORY_COMBAT, true, "Enable line of sight check");
            
            String[] defaultMobs = {"Zombie", "Skeleton", "Creeper", "Spider", "Enderman"};
            String[] mobs = config.getStringList("targetMobs", CATEGORY_COMBAT, defaultMobs, "Target mobs");
            targetMobs = Arrays.asList(mobs);
            
            weaponSlot = config.getInt("weaponSlot", CATEGORY_COMBAT, -1, -1, 8, "Weapon slot (-1 for auto-detect)");
        }
    }
    
    /**
     * Mining module configuration
     */
    public class MiningConfig {
        public boolean enabled;
        public int range;
        public List<String> targetBlocks;
        public int pickaxeSlot;
        public int weaponSlot;
        public boolean mobSpawnDetection;
        
        public MiningConfig() {
            enabled = config.getBoolean("enabled", CATEGORY_MINING, false, "Enable mining module");
            range = config.getInt("range", CATEGORY_MINING, 5, 1, 20, "Mining range in blocks");
            
            String[] defaultBlocks = {"minecraft:diamond_ore", "minecraft:iron_ore", "minecraft:gold_ore", "minecraft:coal_ore"};
            String[] blocks = config.getStringList("targetBlocks", CATEGORY_MINING, defaultBlocks, "Target blocks");
            targetBlocks = Arrays.asList(blocks);
            
            pickaxeSlot = config.getInt("pickaxeSlot", CATEGORY_MINING, -1, -1, 8, "Pickaxe slot (-1 for auto-detect)");
            weaponSlot = config.getInt("weaponSlot", CATEGORY_MINING, -1, -1, 8, "Weapon slot (-1 for auto-detect)");
            mobSpawnDetection = config.getBoolean("mobSpawnDetection", CATEGORY_MINING, true, "Enable mob spawn detection");
        }
    }
    
    /**
     * Farming module configuration
     */
    public class FarmingConfig {
        public boolean enabled;
        public int range;
        public List<String> targetCrops;
        public int toolSlot;
        public boolean replant;
        
        public FarmingConfig() {
            enabled = config.getBoolean("enabled", CATEGORY_FARMING, false, "Enable farming module");
            range = config.getInt("range", CATEGORY_FARMING, 8, 1, 20, "Farming range in blocks");
            
            String[] defaultCrops = {"minecraft:wheat", "minecraft:carrots", "minecraft:potatoes", "minecraft:beetroots"};
            String[] crops = config.getStringList("targetCrops", CATEGORY_FARMING, defaultCrops, "Target crops");
            targetCrops = Arrays.asList(crops);
            
            toolSlot = config.getInt("toolSlot", CATEGORY_FARMING, -1, -1, 8, "Tool slot (-1 for auto-detect)");
            replant = config.getBoolean("replant", CATEGORY_FARMING, true, "Enable replanting");
        }
    }
    
    /**
     * Learning module configuration
     */
    public class LearningConfig {
        public boolean enabled;
        public int updateInterval;
        public boolean behaviorAnalysis;
        public boolean parameterOptimization;
        public boolean routeLearning;
        public boolean antiCheatDetection;
        public double sensitivity;
        
        public LearningConfig() {
            enabled = config.getBoolean("enabled", CATEGORY_LEARNING, false, "Enable learning module");
            updateInterval = config.getInt("updateInterval", CATEGORY_LEARNING, 100, 20, 1000, "Learning update interval (ticks)");
            behaviorAnalysis = config.getBoolean("behaviorAnalysis", CATEGORY_LEARNING, true, "Enable behavior pattern analysis");
            parameterOptimization = config.getBoolean("parameterOptimization", CATEGORY_LEARNING, true, "Enable parameter optimization");
            routeLearning = config.getBoolean("routeLearning", CATEGORY_LEARNING, true, "Enable route learning");
            antiCheatDetection = config.getBoolean("antiCheatDetection", CATEGORY_LEARNING, true, "Enable anti-cheat detection");
            sensitivity = config.getFloat("sensitivity", CATEGORY_LEARNING, 0.5f, 0.1f, 1.0f, "Learning sensitivity");
        }
    }
    
    /**
     * General configuration
     */
    public class GeneralConfig {
        public boolean debug;
        public int guiKey;
        public boolean chatNotifications;
        
        public GeneralConfig() {
            debug = config.getBoolean("debug", CATEGORY_GENERAL, false, "Enable debug mode");
            guiKey = config.getInt("guiKey", CATEGORY_GENERAL, 19, 0, 255, "GUI key code (default: R = 19)");
            chatNotifications = config.getBoolean("chatNotifications", CATEGORY_GENERAL, true, "Enable chat notifications");
        }
    }
}
