package com.newera.gui.widgets;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.renderer.GlStateManager;

/**
 * Custom toggle button widget for the NewEra GUI
 */
public class GuiToggleButton extends GuiButton {
    
    private boolean toggled;
    private final String enabledText;
    private final String disabledText;
    private final int enabledColor;
    private final int disabledColor;
    
    public GuiToggleButton(int id, int x, int y, int width, int height, 
                          String enabledText, String disabledText, boolean initialState) {
        super(id, x, y, width, height, "");
        this.enabledText = enabledText;
        this.disabledText = disabledText;
        this.toggled = initialState;
        this.enabledColor = 0x55FF55; // Green
        this.disabledColor = 0xFF5555; // Red
        updateDisplayString();
    }
    
    public GuiToggleButton(int id, int x, int y, String enabledText, String disabledText, boolean initialState) {
        this(id, x, y, 200, 20, enabledText, disabledText, initialState);
    }
    
    /**
     * Update the display string based on toggle state
     */
    private void updateDisplayString() {
        this.displayString = toggled ? enabledText : disabledText;
    }
    
    /**
     * Toggle the button state
     */
    public void toggle() {
        this.toggled = !this.toggled;
        updateDisplayString();
    }
    
    /**
     * Set the toggle state
     */
    public void setToggled(boolean toggled) {
        this.toggled = toggled;
        updateDisplayString();
    }
    
    /**
     * Get the toggle state
     */
    public boolean isToggled() {
        return toggled;
    }
    
    /**
     * Draw the toggle button with colored background
     */
    @Override
    public void drawButton(Minecraft mc, int mouseX, int mouseY) {
        if (!this.visible) return;
        
        mc.getTextureManager().bindTexture(buttonTextures);
        
        boolean hovered = mouseX >= this.xPosition && mouseY >= this.yPosition && 
                         mouseX < this.xPosition + this.width && mouseY < this.yPosition + this.height;
        
        int textureY = this.getHoverState(hovered);
        
        // Set color based on toggle state
        if (toggled) {
            GlStateManager.color(0.33f, 1.0f, 0.33f, 1.0f); // Green tint
        } else {
            GlStateManager.color(1.0f, 0.33f, 0.33f, 1.0f); // Red tint
        }
        
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(770, 771, 1, 0);
        GlStateManager.blendFunc(770, 771);
        
        // Draw button background
        this.drawTexturedModalRect(this.xPosition, this.yPosition, 0, 46 + textureY * 20, this.width / 2, this.height);
        this.drawTexturedModalRect(this.xPosition + this.width / 2, this.yPosition, 200 - this.width / 2, 46 + textureY * 20, this.width / 2, this.height);
        
        // Reset color
        GlStateManager.color(1.0f, 1.0f, 1.0f, 1.0f);
        
        // Draw text
        int textColor = toggled ? enabledColor : disabledColor;
        
        if (!this.enabled) {
            textColor = 10526880;
        } else if (hovered) {
            // Brighten the color when hovered
            if (toggled) {
                textColor = 0x77FF77;
            } else {
                textColor = 0xFF7777;
            }
        }
        
        this.drawCenteredString(mc.fontRendererObj, this.displayString, 
                               this.xPosition + this.width / 2, 
                               this.yPosition + (this.height - 8) / 2, textColor);
    }
    
    /**
     * Get the enabled text
     */
    public String getEnabledText() {
        return enabledText;
    }
    
    /**
     * Get the disabled text
     */
    public String getDisabledText() {
        return disabledText;
    }
}
